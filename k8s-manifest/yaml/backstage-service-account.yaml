
# 生成 Backstage管理K8S需要的ServiceAccountToken
# https://backstage.io/docs/features/kubernetes/configuration/
# kubectl -n devops get secret backstage-k8s-manager-secret -o go-template='{{.data.token | base64decode}}'

apiVersion: v1
kind: ServiceAccount
metadata:
  name: backstage-k8s-manager
  namespace: devops

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: backstage-k8s-manager
rules:
  - apiGroups:
      - '*'
    resources:
      - pods
      - configmaps
      - secrets
      - services
      - deployments
      - replicasets
      - horizontalpodautoscalers
      - ingresses
      - statefulsets
      - limitranges
      - resourcequotas
      - daemonsets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - batch
    resources:
      - jobs
      - cronjobs
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - metrics.k8s.io
    resources:
      - pods
    verbs:
      - get
      - list
---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: backstage-k8s-manager-binding
subjects:
- kind: ServiceAccount
  name: backstage-k8s-manager
  namespace: devops
roleRef:
  kind: ClusterRole
  name: backstage-k8s-manager
  apiGroup: rbac.authorization.k8s.io

---

apiVersion: v1
kind: Secret
metadata:
  name: backstage-k8s-manager-secret
  namespace: devops
  annotations:
    kubernetes.io/service-account.name: backstage-k8s-manager
type: kubernetes.io/service-account-token








