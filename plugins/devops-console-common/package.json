{"name": "@internal/plugin-devops-console-common", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "Common functionalities for the devops-console plugin", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts"}, "backstage": {"role": "common-library", "pluginId": "devops-console", "pluginPackages": ["@internal/plugin-devops-console", "@internal/plugin-devops-console-common", "@internal/plugin-devops-console-backend"]}, "sideEffects": false, "scripts": {"build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "devDependencies": {"@backstage/cli": "^0.33.0"}, "files": ["dist"]}