import {
  createBackendModule,
} from '@backstage/backend-plugin-api';

import {
  GroupTransformer,
  keycloakTransformerExtensionPoint,
  UserTransformer,
} from '@backstage-community/plugin-catalog-backend-module-keycloak';


const customGroupTransformer: GroupTransformer = async (
  entity,
  group,
  _realm,
) => {
  // metadata.name不允许中文，替换成id
  entity.metadata.name = group.id ?? ''
  entity.metadata.title = group.name ?? ''
  entity.spec.profile = {
    ...entity.spec.profile,
    displayName: group.name ?? ''
  };
  return entity;
};

const customUserTransformer: UserTransformer = async (
  entity,
  user,
  _realm,
  _groups,
) => {
  if (!user.username || user.username.includes('$')) {
    return undefined;
  }
  entity.metadata.name = user.username ?? ''
  entity.spec.profile = {
    ...entity.spec.profile,
    displayName: (user.attributes?.displayName ?? [user.lastName, user.firstName])
      .filter(Boolean).join('') ?? user.username,
  };

  entity.metadata.annotations = {
    ...entity.metadata.annotations,
    'fxiaoke.com/user-phone': `${String(user.attributes?.phoneNumber?.filter(Boolean).join('') ?? '')}`,
    'fxiaoke.com/employee-id': `${String(user.attributes?.employeeID?.filter(Boolean).join('') ?? '')}`,
    'fxiaoke.com/searcher': `${String(user.attributes?.searcher?.filter(Boolean).join('') ?? '')}`,
    'fxiaoke.com/department': `${String(user.attributes?.department?.filter(Boolean).join('') ?? '')}`,
  };
  return entity;
};

export const keycloakBackendModuleTransformer = createBackendModule({
  pluginId: 'catalog',
  moduleId: 'keycloak-transformer',
  register(reg) {
    reg.registerInit({
      deps: { keycloak: keycloakTransformerExtensionPoint },
      async init({ keycloak }) {
        keycloak.setUserTransformer(customUserTransformer);
        keycloak.setGroupTransformer(customGroupTransformer);
      },
    });
  },
});
