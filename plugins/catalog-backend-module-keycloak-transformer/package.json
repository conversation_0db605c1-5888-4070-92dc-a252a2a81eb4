{"name": "@internal/plugin-catalog-backend-module-keycloak-transformer", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "The keycloak-transformer backend module for the catalog plugin.", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin-module"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-plugin-api": "^1.4.0"}, "devDependencies": {"@backstage/backend-test-utils": "^1.6.0", "@backstage/cli": "^0.33.0"}, "files": ["dist"]}