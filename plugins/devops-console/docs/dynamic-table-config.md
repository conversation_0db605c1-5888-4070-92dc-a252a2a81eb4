# 动态表格配置指南

## 概述

动态表格组件允许用户通过JSON配置来创建功能完整的数据表格，支持分页、搜索、排序、过滤等功能。

## 配置结构

### 基本配置

```json
{
  "id": "unique-table-id",
  "name": "表格名称",
  "description": "表格描述",
  "columns": [...],
  "api": {...},
  "search": {...},
  "pagination": {...},
  "sort": {...},
  "options": {...}
}
```

### 列配置 (columns)

```json
{
  "title": "列标题",
  "field": "数据字段名",
  "type": "text|number|boolean|date|enum|custom",
  "searchable": true,
  "sortable": true,
  "filterable": true,
  "width": 120,
  "maxWidth": "400px",
  "hidden": false,
  "renderFunction": "自定义渲染函数名",
  "enumOptions": [
    {"label": "显示文本", "value": "实际值"}
  ],
  "format": {
    "dateFormat": "YYYY-MM-DD",
    "precision": 2,
    "prefix": "￥",
    "suffix": "元"
  },
  "style": {
    "textAlign": "left|center|right",
    "ellipsis": true,
    "className": "custom-css-class"
  }
}
```

### API配置 (api)

```json
{
  "endpoint": "/api/data",
  "method": "GET|POST|PUT|DELETE",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyTemplate": {},
  "dataPath": "results",
  "totalCountPath": "totalCount",
  "paginationMapping": {
    "pageField": "pageNum",
    "pageSizeField": "pageSize",
    "pageStartsFrom": 1
  },
  "searchMapping": {
    "field": "search",
    "operator": "eq|like|contains|startsWith|endsWith"
  },
  "sortMapping": {
    "fieldParam": "sortBy",
    "directionParam": "sortOrder",
    "ascValue": "asc",
    "descValue": "desc"
  }
}
```

### 搜索配置 (search)

```json
{
  "enabled": true,
  "placeholder": "搜索提示文本",
  "fieldMapping": "搜索字段映射",
  "debounceMs": 800
}
```

### 分页配置 (pagination)

```json
{
  "enabled": true,
  "defaultPageSize": 10,
  "pageSizeOptions": [10, 20, 50, 100],
  "showFirstLastButtons": true
}
```

### 排序配置 (sort)

```json
{
  "defaultField": "默认排序字段",
  "defaultDirection": "asc|desc",
  "multiSort": false
}
```

### 表格选项 (options)

```json
{
  "showRowNumber": false,
  "selectable": false,
  "doubleClickEnabled": true,
  "emptyMessage": "没有数据",
  "loadingType": "linear|circular",
  "density": "default|dense|comfortable"
}
```

## 配置示例

### 1. 短信记录表格

```json
{
  "id": "sms-records",
  "name": "短信记录查询",
  "description": "查询和管理短信发送记录",
  "columns": [
    {
      "title": "手机号",
      "field": "phone",
      "type": "text",
      "searchable": true,
      "filterable": true,
      "width": 120
    },
    {
      "title": "内容",
      "field": "content",
      "type": "text",
      "filterable": true,
      "maxWidth": "400px",
      "style": {
        "ellipsis": true
      }
    },
    {
      "title": "发送状态",
      "field": "status",
      "type": "boolean",
      "filterable": true,
      "renderFunction": "statusRenderer",
      "width": 100
    }
  ],
  "api": {
    "endpoint": "/egress-api-service/api/v2/sms/sms/histories",
    "method": "POST",
    "dataPath": "results",
    "totalCountPath": "totalCount",
    "paginationMapping": {
      "pageField": "pageNum",
      "pageSizeField": "pageSize",
      "pageStartsFrom": 1
    },
    "searchMapping": {
      "field": "phone",
      "operator": "eq"
    }
  },
  "search": {
    "enabled": true,
    "placeholder": "按手机号搜索",
    "debounceMs": 800
  },
  "pagination": {
    "enabled": true,
    "defaultPageSize": 10,
    "pageSizeOptions": [10, 20, 50, 100]
  },
  "options": {
    "doubleClickEnabled": true,
    "emptyMessage": "没有找到匹配的记录",
    "loadingType": "linear",
    "density": "dense"
  }
}
```

### 2. 用户管理表格

```json
{
  "id": "user-management",
  "name": "用户管理",
  "description": "系统用户信息管理",
  "columns": [
    {
      "title": "用户名",
      "field": "username",
      "type": "text",
      "searchable": true,
      "filterable": true
    },
    {
      "title": "状态",
      "field": "status",
      "type": "enum",
      "filterable": true,
      "enumOptions": [
        {"label": "激活", "value": "active"},
        {"label": "禁用", "value": "disabled"},
        {"label": "待激活", "value": "pending"}
      ]
    }
  ],
  "api": {
    "endpoint": "/api/users",
    "method": "GET",
    "dataPath": "data",
    "totalCountPath": "total",
    "paginationMapping": {
      "pageField": "page",
      "pageSizeField": "limit"
    },
    "searchMapping": {
      "field": "search",
      "operator": "contains"
    }
  }
}
```

## 自定义渲染器

可以通过注册自定义渲染器来实现特殊的列显示效果：

```typescript
const customRenderers: CustomRenderer[] = [
  {
    name: 'statusRenderer',
    render: (value: boolean) => (
      <span style={{ color: value ? 'green' : 'red' }}>
        {value ? '成功' : '失败'}
      </span>
    ),
  },
];
```

## 使用方式

```typescript
import { DynamicTable } from './components/DynamicTable';

<DynamicTable
  config={tableConfig}
  customRenderers={customRenderers}
  height="600px"
  autoLoad={true}
/>
```

## 注意事项

1. **API端点**: 确保API端点可访问且返回正确格式的数据
2. **数据路径**: 正确配置`dataPath`和`totalCountPath`以提取响应数据
3. **分页映射**: 根据后端API的分页参数格式配置`paginationMapping`
4. **搜索映射**: 根据后端API的搜索参数格式配置`searchMapping`
5. **自定义渲染**: 使用`renderFunction`时需要注册对应的自定义渲染器
