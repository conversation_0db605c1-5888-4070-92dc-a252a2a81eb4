/**
 * 动态表格配置类型定义
 */

// 列配置类型
export interface DynamicColumnConfig {
  /** 列标题 */
  title: string;
  /** 字段名 */
  field: string;
  /** 列类型 */
  type?: 'text' | 'number' | 'boolean' | 'date' | 'enum' | 'custom';
  /** 是否可搜索 */
  searchable?: boolean;
  /** 是否可排序 */
  sortable?: boolean;
  /** 是否可过滤 */
  filterable?: boolean;
  /** 列宽度 */
  width?: number | string;
  /** 最大宽度 */
  maxWidth?: number | string;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 自定义渲染函数名称 */
  renderFunction?: string;
  /** 枚举值选项（当type为enum时使用） */
  enumOptions?: Array<{ label: string; value: any }>;
  /** 格式化选项 */
  format?: {
    /** 日期格式 */
    dateFormat?: string;
    /** 数字精度 */
    precision?: number;
    /** 前缀 */
    prefix?: string;
    /** 后缀 */
    suffix?: string;
  };
  /** 样式配置 */
  style?: {
    /** 文本对齐 */
    textAlign?: 'left' | 'center' | 'right';
    /** 文本省略 */
    ellipsis?: boolean;
    /** 自定义CSS类名 */
    className?: string;
  };
}

// 搜索配置
export interface SearchConfig {
  /** 是否启用搜索 */
  enabled: boolean;
  /** 搜索占位符 */
  placeholder?: string;
  /** 搜索字段映射 */
  fieldMapping?: string;
  /** 搜索延迟（毫秒） */
  debounceMs?: number;
}

// 分页配置
export interface PaginationConfig {
  /** 是否启用分页 */
  enabled: boolean;
  /** 默认页大小 */
  defaultPageSize: number;
  /** 页大小选项 */
  pageSizeOptions: number[];
  /** 是否显示首页/末页按钮 */
  showFirstLastButtons?: boolean;
}

// 排序配置
export interface SortConfig {
  /** 默认排序字段 */
  defaultField?: string;
  /** 默认排序方向 */
  defaultDirection?: 'asc' | 'desc';
  /** 是否支持多字段排序 */
  multiSort?: boolean;
}

// API配置
export interface ApiConfig {
  /** API端点URL */
  endpoint: string;
  /** HTTP方法 */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求体模板 */
  bodyTemplate?: Record<string, any>;
  /** 响应数据路径 */
  dataPath?: string;
  /** 总数路径 */
  totalCountPath?: string;
  /** 分页参数映射 */
  paginationMapping?: {
    pageField: string;
    pageSizeField: string;
    pageStartsFrom?: number; // 页码起始值，默认1
  };
  /** 搜索参数映射 */
  searchMapping?: {
    field: string;
    operator?: 'eq' | 'like' | 'contains' | 'startsWith' | 'endsWith';
  };
  /** 排序参数映射 */
  sortMapping?: {
    fieldParam: string;
    directionParam: string;
    ascValue?: string;
    descValue?: string;
  };
}

// 表格配置
export interface DynamicTableConfig {
  /** 配置ID */
  id: string;
  /** 表格名称 */
  name: string;
  /** 表格描述 */
  description?: string;
  /** 列配置 */
  columns: DynamicColumnConfig[];
  /** API配置 */
  api: ApiConfig;
  /** 搜索配置 */
  search?: SearchConfig;
  /** 分页配置 */
  pagination?: PaginationConfig;
  /** 排序配置 */
  sort?: SortConfig;
  /** 表格选项 */
  options?: {
    /** 是否显示行号 */
    showRowNumber?: boolean;
    /** 是否支持行选择 */
    selectable?: boolean;
    /** 是否支持双击行事件 */
    doubleClickEnabled?: boolean;
    /** 空数据提示 */
    emptyMessage?: string;
    /** 加载类型 */
    loadingType?: 'linear' | 'circular';
    /** 表格密度 */
    density?: 'default' | 'dense' | 'comfortable';
  };
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 创建者 */
  createdBy?: string;
}

// 查询参数
export interface DynamicTableQueryParams {
  /** 页码 */
  page?: number;
  /** 页大小 */
  pageSize?: number;
  /** 搜索关键词 */
  search?: string;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向 */
  sortDirection?: 'asc' | 'desc';
  /** 其他过滤参数 */
  filters?: Record<string, any>;
}

// API响应
export interface DynamicTableResponse {
  /** 数据列表 */
  data: any[];
  /** 总数 */
  total: number;
  /** 当前页 */
  page?: number;
  /** 页大小 */
  pageSize?: number;
  /** 总页数 */
  totalPages?: number;
}

// 自定义渲染器注册
export interface CustomRenderer {
  /** 渲染器名称 */
  name: string;
  /** 渲染函数 */
  render: (value: any, rowData: any, column: DynamicColumnConfig) => React.ReactNode;
}

// 表格状态
export interface DynamicTableState {
  /** 数据 */
  data: any[];
  /** 总数 */
  total: number;
  /** 当前页 */
  page: number;
  /** 页大小 */
  pageSize: number;
  /** 搜索关键词 */
  searchText: string;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向 */
  sortDirection?: 'asc' | 'desc';
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error?: Error;
  /** 选中的行 */
  selectedRows: any[];
}
