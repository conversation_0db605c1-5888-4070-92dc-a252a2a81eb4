import { useState, useEffect, useRef } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';

import { SmsRecord } from '../../api';
import { Typography } from '@material-ui/core';
import { JsonViewerDialog } from '../JsonViewer';

const useStyles = makeStyles({
  statusDelivered: {
    color: 'green',
  },
  statusFailed: {
    color: 'red',
  },
});

type SmsTableProps = {
  smsRecords: SmsRecord[];
  totalCount: number;
  page: number;
  pageSize: number;
  loading: boolean;
  error?: Error;
  onChangePage: (page: number) => void;
  onChangeRowsPerPage: (pageSize: number) => void;
  onSearch?: (searchText: string) => void;
  initialSearchText?: string;
};

export const SmsTable = ({
  smsRecords,
  totalCount,
  page,
  pageSize,
  loading,
  error,
  onChangePage,
  onChangeRowsPerPage,
  onSearch,
  initialSearchText = '',
}: SmsTableProps) => {
  const classes = useStyles();

  // 用于延迟搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于存储选中行的数据和对话框状态
  const [selectedRow, setSelectedRow] = useState<SmsRecord | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理搜索输入变化，使用防抖
  const handleSearchChange = (searchText: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，延迟800ms触发搜索
    if (onSearch) {
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(searchText);
      }, 800);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const columns: TableColumn[] = [
    {
      title: '手机',
      field: 'phone',
      filtering: true,
    },
    {
      title: '内容',
      field: 'content',
      filtering: true,
      cellStyle: {
        maxWidth: '400px',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      },
      render: (rowData: any) => (
        <div
          style={{
            maxWidth: '400px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
          title={rowData.content || ''}
        >
          {rowData.content || ''}
        </div>
      )
    },
    {
      title: '企业ID',
      field: 'enterpriseId',
      filtering: true,
      headerStyle: { width: '120px' },
      render: (rowData: any) => {
        const enterpriseId = rowData.enterpriseId || '';
        return (
          <div
            style={{
              maxWidth: '120px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
            title={enterpriseId}
          >
            {enterpriseId}
          </div>
        );
      }
    },
    {
      title: '发送状态',
      field: 'status',
      filtering: true,
      render: (row: any) => (
        <span className={row.status ? classes.statusDelivered : classes.statusFailed}>
          {row.status ? '成功' : '失败'}
        </span>
      ),
      headerStyle: { padding: '8px 16px' },
    },
    {
      title: '服务商',
      field: 'providerName',
      filtering: true,
      headerStyle: { width: '120px' },
    },
    {
      title: '发送时间',
      field: 'sendTime',
      filtering: true,
      headerStyle: { width: '160px' },
    },
  ];
  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  const handleRowClick = (_event?: React.MouseEvent, rowData?: any) => {
    if (rowData) {
      setSelectedRow(rowData as SmsRecord);
      setDialogOpen(true);
    }
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  return (
    <>
      <Table
        title="短信记录"
        isLoading={loading}
        options={{
          search: true,
          paging: true,
          pageSize,
          pageSizeOptions: [10, 20, 50, 100, 1000],
          padding: 'dense',
          showFirstLastPageButtons: true,
          searchText: initialSearchText,
          searchAutoFocus: true,
          loadingType: 'linear',
        }}
        localization={{
          toolbar: {
            searchPlaceholder: '手机号精确搜索',
          },
        }}
        data={smsRecords}
        columns={columns}
        totalCount={totalCount}
        page={page - 1} // 转换为 0-based 索引
        onPageChange={newPage => {
          // 计算实际的页码（1-based）
          onChangePage(newPage + 1);
        }}
        onRowsPerPageChange={onChangeRowsPerPage}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            没有找到匹配的记录
          </Typography>
        }
      />

      {/* JSON 查看器对话框 */}
      <JsonViewerDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        title="短信详细信息"
        data={selectedRow}
        maxWidth="md"
      />
    </>
  );
};
