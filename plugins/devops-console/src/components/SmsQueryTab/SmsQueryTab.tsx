import { useState, useEffect } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { SmsTable } from './SmsTable';
import { smsApiRef, SmsQueryParams, SmsRecord } from '../../api';

export const SmsQueryTab = () => {
  const smsApi = useApi(smsApiRef);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);
  const [smsRecords, setSmsRecords] = useState<SmsRecord[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  const fetchSmsRecords = async () => {
    setLoading(true);
    setError(undefined);

    try {
      const queryParams: SmsQueryParams = {
        pageNum: page,
        pageSize,
      };

      if (searchQuery.trim()) {
        queryParams.phone = searchQuery.trim();
      }

      const response = await smsApi.querySmsRecords(queryParams);
      setSmsRecords(response.results);
      setTotalCount(response.totalCount);
    } catch (err) {
      setError(err as Error);
      setSmsRecords([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSmsRecords();
  }, [page, pageSize, searchQuery]);

  const handleSearch = (searchText: string) => {
    if (searchText !== searchQuery) {
      setPage(1); // 搜索时重置到第一页
    }
    setSearchQuery(searchText);
  };

  return (
    <div>
      <SmsTable
        smsRecords={smsRecords}
        totalCount={totalCount}
        page={page}
        pageSize={pageSize}
        loading={loading}
        error={error}
        onChangePage={setPage}
        onChangeRowsPerPage={setPageSize}
        onSearch={handleSearch}
        initialSearchText={searchQuery}
      />
    </div>
  );
};
