import { useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Typography,
  Box,
  Tooltip,
} from '@material-ui/core';
import { makeStyles, Theme } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import CopyIcon from '@material-ui/icons/FileCopy';
import { useApi, alertApiRef } from '@backstage/core-plugin-api';

const useStyles = makeStyles((theme: Theme) => ({
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: theme.spacing(1),
    background: `linear-gradient(45deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  titleText: {
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialogContent: {
    padding: 0,
    backgroundColor: theme.palette.background.default,
  },
  jsonContainer: {
    position: 'relative',
    backgroundColor: theme.palette.type === 'dark' ? '#1e1e1e' : '#f8f9fa',
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    overflow: 'hidden',
  },
  jsonHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    backgroundColor: theme.palette.type === 'dark' ? '#2d2d2d' : '#ffffff',
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  jsonContent: {
    fontFamily: '"Fira Code", "Monaco", "Menlo", "Ubuntu Mono", monospace',
    fontSize: '13px',
    lineHeight: 1.5,
    padding: theme.spacing(2),
    margin: 0,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    color: theme.palette.text.primary,
    backgroundColor: 'transparent',
    maxHeight: '60vh',
    overflow: 'auto',
    '&::-webkit-scrollbar': {
      width: '8px',
      height: '8px',
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: theme.palette.action.hover,
      borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: theme.palette.action.selected,
      borderRadius: '4px',
      '&:hover': {
        backgroundColor: theme.palette.action.focus,
      },
    },
  },
  dialogActions: {
    padding: theme.spacing(1, 3, 2),
    backgroundColor: theme.palette.background.paper,
    borderTop: `1px solid ${theme.palette.divider}`,
  },
  copyButton: {
    marginRight: theme.spacing(1),
  },
  sizeChip: {
    fontSize: '11px',
    height: '20px',
  },
}));

interface JsonViewerDialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  data: any;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export const JsonViewerDialog: React.FC<JsonViewerDialogProps> = ({
  open,
  onClose,
  title = 'JSON 数据查看器',
  data,
  maxWidth = 'md',
}) => {
  const classes = useStyles();
  const alertApi = useApi(alertApiRef);

  const jsonString = useMemo(() => {
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Invalid JSON data';
    }
  }, [data]);

  const handleCopyJson = async () => {
    try {
      await navigator.clipboard.writeText(jsonString);
      alertApi.post({
        message: '数据已复制到剪贴板',
        severity: 'success',
        display: 'transient'
      });
    } catch (error) {
      alertApi.post({
        message: '复制失败，请手动选择并复制',
        severity: 'error',
        display: 'transient'
      });
    }
  };

  const handleCopyThenClose = async () => {
    handleCopyJson();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth
      aria-labelledby="json-viewer-dialog-title"
    >
      <DialogTitle id="json-viewer-dialog-title" className={classes.dialogTitle}>
        <Typography variant="h6" className={classes.titleText}>
          {title}
        </Typography>
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent className={classes.dialogContent}>
        <Box p={1}>
          <div className={classes.jsonContainer}>
            <div className={classes.jsonHeader}>
              <Typography variant="body2" color="textSecondary">
                JSON
              </Typography>
              <Tooltip title="复制 JSON">
                <IconButton size="small" onClick={handleCopyJson}>
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </div>
            <pre className={classes.jsonContent}>
              {jsonString}
            </pre>
          </div>
        </Box>
      </DialogContent>

      <DialogActions className={classes.dialogActions}>
        <Button onClick={handleCopyThenClose} color="primary" variant="contained" className={classes.copyButton}>
          复制并关闭
        </Button>
        <Button onClick={onClose} color="primary">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};
