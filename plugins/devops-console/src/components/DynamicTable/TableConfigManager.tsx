import { useState, useEffect } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import {
  Card,
  CardContent,
  CardHeader,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Chip,
  makeStyles,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import ViewIcon from '@material-ui/icons/Visibility';

import { DynamicTableConfig } from '../../types/DynamicTable';
import { dynamicTableApiRef } from '../../api/DynamicTableApi';
import { DynamicTable } from './DynamicTable';

const useStyles = makeStyles(theme => ({
  container: {
    padding: theme.spacing(2),
  },
  configCard: {
    marginBottom: theme.spacing(2),
  },
  configList: {
    maxHeight: '400px',
    overflow: 'auto',
  },
  previewDialog: {
    '& .MuiDialog-paper': {
      maxWidth: '90vw',
      maxHeight: '90vh',
    },
  },
  chipContainer: {
    display: 'flex',
    gap: theme.spacing(0.5),
    flexWrap: 'wrap',
    marginTop: theme.spacing(1),
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
}));

interface TableConfigManagerProps {
  /** 选中配置时的回调 */
  onConfigSelect?: (config: DynamicTableConfig) => void;
  /** 是否显示预览功能 */
  showPreview?: boolean;
}

export const TableConfigManager = ({
  onConfigSelect,
  showPreview = true,
}: TableConfigManagerProps) => {
  const classes = useStyles();
  const dynamicTableApi = useApi(dynamicTableApiRef);

  const [configs, setConfigs] = useState<DynamicTableConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 预览对话框状态
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewConfig, setPreviewConfig] = useState<DynamicTableConfig | null>(null);

  // 编辑对话框状态
  const [editOpen, setEditOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<DynamicTableConfig | null>(null);
  const [configJson, setConfigJson] = useState('');

  // 加载配置列表
  const loadConfigs = async () => {
    setLoading(true);
    setError('');

    try {
      const configList = await dynamicTableApi.getAllTableConfigs();
      setConfigs(configList);
    } catch (err) {
      setError(`加载配置失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // 处理配置选择
  const handleConfigSelect = (config: DynamicTableConfig) => {
    if (onConfigSelect) {
      onConfigSelect(config);
    }
  };

  // 处理预览
  const handlePreview = (config: DynamicTableConfig) => {
    setPreviewConfig(config);
    setPreviewOpen(true);
  };

  // 处理编辑
  const handleEdit = (config: DynamicTableConfig) => {
    setEditingConfig(config);
    setConfigJson(JSON.stringify(config, null, 2));
    setEditOpen(true);
  };

  // 处理新建
  const handleCreate = () => {
    const newConfig: DynamicTableConfig = {
      id: `config_${Date.now()}`,
      name: '新建表格',
      description: '',
      columns: [],
      api: {
        endpoint: '',
        method: 'GET',
      },
      search: {
        enabled: true,
        placeholder: '搜索...',
        debounceMs: 800,
      },
      pagination: {
        enabled: true,
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        showFirstLastButtons: true,
      },
      options: {
        doubleClickEnabled: true,
        emptyMessage: '没有找到匹配的记录',
        loadingType: 'linear',
        density: 'default',
      },
    };

    setEditingConfig(newConfig);
    setConfigJson(JSON.stringify(newConfig, null, 2));
    setEditOpen(true);
  };

  // 处理保存
  const handleSave = async () => {
    if (!editingConfig) return;

    try {
      const config = JSON.parse(configJson);
      config.updatedAt = new Date().toISOString();

      await dynamicTableApi.saveTableConfig(config);
      await loadConfigs();
      setEditOpen(false);
      setEditingConfig(null);
      setConfigJson('');
    } catch (err) {
      setError(`保存配置失败: ${err instanceof Error ? err.message : '配置格式错误'}`);
    }
  };

  // 处理删除
  const handleDelete = async (config: DynamicTableConfig) => {
    // eslint-disable-next-line no-alert
    if (!window.confirm(`确定要删除配置 "${config.name}" 吗？`)) {
      return;
    }

    try {
      await dynamicTableApi.deleteTableConfig(config.id);
      await loadConfigs();
    } catch (err) {
      setError(`删除配置失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  // 渲染配置信息
  const renderConfigInfo = (config: DynamicTableConfig) => {
    return (
      <>
        <Typography variant="body2" color="textSecondary">
          {config.description || '无描述'}
        </Typography>
        <div className={classes.chipContainer}>
          <Chip
            size="small"
            label={`${config.columns.length} 列`}
            color="primary"
            variant="outlined"
          />
          <Chip
            size="small"
            label={config.api.method}
            color="secondary"
            variant="outlined"
          />
          {config.search?.enabled && (
            <Chip
              size="small"
              label="搜索"
              variant="outlined"
            />
          )}
          {config.pagination?.enabled && (
            <Chip
              size="small"
              label="分页"
              variant="outlined"
            />
          )}
        </div>
      </>
    );
  };

  return (
    <div className={classes.container}>
      <Card className={classes.configCard}>
        <CardHeader
          title="表格配置管理"
          action={
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreate}
            >
              新建配置
            </Button>
          }
        />
        <CardContent>
          {error && (
            <Typography color="error" style={{ marginBottom: 16 }}>
              {error}
            </Typography>
          )}

          {loading && (
            <Typography>加载中...</Typography>
          )}
          {!loading && configs.length === 0 && (
            <Typography color="textSecondary">
              暂无配置，点击"新建配置"开始创建
            </Typography>
          )}
          {!loading && configs.length > 0 && (
            <List className={classes.configList}>
              {configs.map((config) => (
                <ListItem key={config.id} divider>
                  <ListItemText
                    primary={config.name}
                    secondary={renderConfigInfo(config)}
                  />
                  <ListItemSecondaryAction>
                    <div className={classes.actionButtons}>
                      {showPreview && (
                        <IconButton
                          edge="end"
                          aria-label="预览"
                          onClick={() => handlePreview(config)}
                        >
                          <ViewIcon />
                        </IconButton>
                      )}
                      <IconButton
                        edge="end"
                        aria-label="编辑"
                        onClick={() => handleEdit(config)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        aria-label="删除"
                        onClick={() => handleDelete(config)}
                      >
                        <DeleteIcon />
                      </IconButton>
                      {onConfigSelect && (
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => handleConfigSelect(config)}
                        >
                          选择
                        </Button>
                      )}
                    </div>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* 预览对话框 */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        className={classes.previewDialog}
        maxWidth={false}
      >
        <DialogTitle>
          预览表格: {previewConfig?.name}
        </DialogTitle>
        <DialogContent>
          {previewConfig && (
            <DynamicTable
              config={previewConfig}
              height="60vh"
              autoLoad={false}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog
        open={editOpen}
        onClose={() => setEditOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingConfig?.id.startsWith('config_') ? '新建配置' : '编辑配置'}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            minRows={20}
            maxRows={20}
            variant="outlined"
            label="配置JSON"
            value={configJson}
            onChange={(e) => setConfigJson(e.target.value)}
            style={{ marginTop: 16 }}
            helperText="请输入有效的JSON格式配置"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditOpen(false)}>
            取消
          </Button>
          <Button onClick={handleSave} color="primary" variant="contained">
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
