import { useState, useEffect, useRef, useMemo } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { makeStyles } from '@material-ui/core/styles';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { Typography } from '@material-ui/core';

import {
  DynamicTableConfig,
  DynamicTableState,
  DynamicTableQueryParams,
  CustomRenderer,
} from '../../types/DynamicTable';
import { dynamicTableApiRef } from '../../api/DynamicTableApi';
import { JsonViewerDialog } from '../JsonViewer';

const useStyles = makeStyles(() => ({
  container: {
    width: '100%',
  },
}));

interface DynamicTableProps {
  /** 表格配置 */
  config: DynamicTableConfig;
  /** 自定义渲染器 */
  customRenderers?: CustomRenderer[];
  /** 初始查询参数 */
  initialParams?: Partial<DynamicTableQueryParams>;
  /** 表格高度 */
  height?: number | string;
  /** 是否自动加载数据 */
  autoLoad?: boolean;
}

export const DynamicTable = ({
  config,
  customRenderers = [],
  initialParams = {},
  height,
  autoLoad = true,
}: DynamicTableProps) => {
  const classes = useStyles();
  const dynamicTableApi = useApi(dynamicTableApiRef);

  // 搜索防抖定时器
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 表格状态
  const [state, setState] = useState<DynamicTableState>({
    data: [],
    total: 0,
    page: 1,
    pageSize: config.pagination?.defaultPageSize || 10,
    searchText: '',
    loading: false,
    selectedRows: [],
    ...initialParams,
  });

  // 详情对话框状态
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 创建自定义渲染器映射
  const rendererMap = useMemo(() => {
    const map = new Map<string, CustomRenderer['render']>();
    customRenderers.forEach(renderer => {
      map.set(renderer.name, renderer.render);
    });
    return map;
  }, [customRenderers]);

  // 获取表格数据
  const fetchData = async (params?: Partial<DynamicTableQueryParams>) => {
    setState(prev => ({ ...prev, loading: true, error: undefined }));

    try {
      const queryParams: DynamicTableQueryParams = {
        page: state.page,
        pageSize: state.pageSize,
        search: state.searchText,
        sortField: state.sortField,
        sortDirection: state.sortDirection,
        ...params,
      };

      const response = await dynamicTableApi.queryTableData(config.api, queryParams);

      setState(prev => ({
        ...prev,
        data: response.data,
        total: response.total,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error as Error,
        data: [],
        total: 0,
        loading: false,
      }));
    }
  };

  // 初始加载
  useEffect(() => {
    if (autoLoad) {
      fetchData();
    }
  }, [autoLoad]);

  // 页码或页大小变化时重新加载
  useEffect(() => {
    if (autoLoad) {
      fetchData();
    }
  }, [state.page, state.pageSize]);

  // 搜索变化时重新加载（带防抖）
  useEffect(() => {
    if (!autoLoad) return;

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    const debounceMs = config.search?.debounceMs || 800;
    searchTimeoutRef.current = setTimeout(() => {
      if (state.page !== 1) {
        setState(prev => ({ ...prev, page: 1 }));
      } else {
        fetchData();
      }
    }, debounceMs);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [state.searchText, autoLoad]);

  // 排序变化时重新加载
  useEffect(() => {
    if (autoLoad && (state.sortField || state.sortDirection)) {
      fetchData();
    }
  }, [state.sortField, state.sortDirection, autoLoad]);

  // 构建表格列配置
  const columns: TableColumn[] = useMemo(() => {
    return config.columns
      .filter(col => !col.hidden)
      .map(col => {
        const column: TableColumn = {
          title: col.title,
          field: col.field,
          filtering: col.filterable,
          sorting: col.sortable,
        };

        // 设置列宽
        if (col.width) {
          column.width = typeof col.width === 'number' ? col.width.toString() : col.width;
        }
        if (col.maxWidth) {
          column.headerStyle = { ...column.headerStyle, maxWidth: col.maxWidth };
          column.cellStyle = { ...column.cellStyle, maxWidth: col.maxWidth };
        }

        // 设置样式
        if (col.style) {
          const cellStyle: any = { ...column.cellStyle };

          if (col.style.textAlign) {
            cellStyle.textAlign = col.style.textAlign;
          }

          if (col.style.ellipsis) {
            cellStyle.whiteSpace = 'nowrap';
            cellStyle.overflow = 'hidden';
            cellStyle.textOverflow = 'ellipsis';
          }

          column.cellStyle = cellStyle;
        }

        // 设置自定义渲染
        if (col.renderFunction && rendererMap.has(col.renderFunction)) {
          const renderer = rendererMap.get(col.renderFunction)!;
          column.render = (rowData: any) => renderer(rowData[col.field], rowData, col);
        } else if (col.type) {
          // 根据类型设置默认渲染
          column.render = (rowData: any) => {
            const value = rowData[col.field];
            return renderCellValue(value, col);
          };
        }

        return column;
      });
  }, [config.columns, rendererMap]);

  // 渲染单元格值
  const renderCellValue = (value: any, col: any) => {
    if (value === null || value === undefined) {
      return '';
    }

    switch (col.type) {
      case 'boolean':
        return value ? '是' : '否';

      case 'date':
        if (col.format?.dateFormat) {
          // 这里可以使用日期格式化库，如 date-fns 或 moment.js
          return new Date(value).toLocaleDateString();
        }
        return value;

      case 'number':
        if (col.format?.precision !== undefined) {
          return Number(value).toFixed(col.format.precision);
        }
        return value;

      case 'enum':
        if (col.enumOptions) {
          const option = col.enumOptions.find((opt: any) => opt.value === value);
          return option ? option.label : value;
        }
        return value;

      default: {
        let result = String(value);

        if (col.format?.prefix) {
          result = col.format.prefix + result;
        }
        if (col.format?.suffix) {
          result = result + col.format.suffix;
        }

        // 处理文本省略
        if (col.style?.ellipsis && col.maxWidth) {
          return (
            <div
              style={{
                maxWidth: col.maxWidth,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
              title={result}
            >
              {result}
            </div>
          );
        }

        return result;
      }
    }
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setState(prev => ({ ...prev, page: newPage + 1 }));
  };

  // 处理页大小变化
  const handleRowsPerPageChange = (newPageSize: number) => {
    setState(prev => ({ ...prev, pageSize: newPageSize, page: 1 }));
  };

  // 处理搜索变化
  const handleSearchChange = (searchText: string) => {
    setState(prev => ({ ...prev, searchText }));
  };

  // 处理行双击
  const handleRowDoubleClick = (_event?: React.MouseEvent, rowData?: any) => {
    if (config.options?.doubleClickEnabled && rowData) {
      setSelectedRow(rowData);
      setDialogOpen(true);
    }
  };

  // 处理对话框关闭
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRow(null);
  };

  if (state.error) {
    return <ResponseErrorPanel error={state.error} />;
  }

  return (
    <div className={classes.container} style={{ height }}>
      <Table
        title={config.name}
        isLoading={state.loading}
        options={{
          search: config.search?.enabled || true,
          paging: config.pagination?.enabled || true,
          pageSize: state.pageSize,
          pageSizeOptions: config.pagination?.pageSizeOptions || [10, 20, 50, 100],
          padding: 'dense',
          showFirstLastPageButtons: config.pagination?.showFirstLastButtons || true,
          searchText: state.searchText,
          searchAutoFocus: true,
          loadingType: 'linear',
          selection: config.options?.selectable || false,
        }}
        localization={{
          toolbar: {
            searchPlaceholder: config.search?.placeholder || '搜索...',
          },
        }}
        data={state.data}
        columns={columns}
        totalCount={state.total}
        page={state.page - 1}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowDoubleClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            {config.options?.emptyMessage || '没有找到匹配的记录'}
          </Typography>
        }
      />

      {/* 详情对话框 */}
      {config.options?.doubleClickEnabled && (
        <JsonViewerDialog
          open={dialogOpen}
          onClose={handleDialogClose}
          title={`${config.name} - 详细信息`}
          data={selectedRow}
          maxWidth="md"
        />
      )}
    </div>
  );
};
