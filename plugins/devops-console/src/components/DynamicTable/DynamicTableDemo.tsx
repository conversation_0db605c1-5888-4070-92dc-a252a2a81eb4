import { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  Box,
  Typography,
  makeStyles,
} from '@material-ui/core';

import { DynamicTableConfig, CustomRenderer } from '../../types/DynamicTable';
import { DynamicTable } from './DynamicTable';

const useStyles = makeStyles(theme => ({
  container: {
    padding: theme.spacing(1),
  },
  tabContent: {
    marginTop: theme.spacing(1),
  },
}));

// 示例配置：短信记录表格，此表格从后端配置获取
const smsTableConfig: DynamicTableConfig = {
  id: 'sms-records',
  name: '短信记录',
  description: '查询和管理短信发送记录',
  columns: [
    {
      title: '手机号',
      field: 'phone',
      type: 'text',
      searchable: true,
      filterable: true,
      width: '120px',
    },
    {
      title: '内容',
      field: 'content',
      type: 'text',
      filterable: true,
      maxWidth: '400px',
      style: {
        ellipsis: true,
      },
    },
    {
      title: '企业ID',
      field: 'enterpriseId',
      type: 'text',
      filterable: true,
      width: 120,
      maxWidth: '300px',
      style: {
        ellipsis: true,
      },
    },
    {
      title: '发送状态',
      field: 'status',
      type: 'boolean',
      filterable: true,
      renderFunction: 'statusRenderer',
      width: 100,
    },
    {
      title: '服务商',
      field: 'providerName',
      type: 'text',
      filterable: true,
      width: 120,
    },
    {
      title: '发送时间',
      field: 'sendTime',
      type: 'date',
      filterable: true,
      width: 160,
    },
  ],
  api: {
    endpoint: '/egress-api-service/api/v2/sms/histories',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    bodyTemplate: {},
    // 这些都做成规范，固定值
    dataPath: 'results',
    totalCountPath: 'totalCount',
    paginationMapping: {
      pageField: 'pageNum',
      pageSizeField: 'pageSize',
      pageStartsFrom: 1,
    },
    searchMapping: {
      field: 'phone',
      operator: 'eq',
    },
  },
  search: {
    enabled: true,
    placeholder: '按手机号搜索',
    debounceMs: 800,
    fieldMapping: 'phone',
  },
  pagination: {
    enabled: true,
    defaultPageSize: 10,
    pageSizeOptions: [10, 20, 50, 100, 1000],
    showFirstLastButtons: true,
  },
  sort: {
    defaultField: 'sendTime',
    defaultDirection: 'desc',
  },
  options: {
    doubleClickEnabled: true,
    emptyMessage: '没有找到匹配的记录',
    loadingType: 'linear',
    density: 'dense',
    selectable: false,
  },
};

// 示例配置：用户管理表格
const userTableConfig: DynamicTableConfig = {
  id: 'user-management',
  name: '用户管理',
  description: '系统用户信息管理',
  columns: [
    {
      title: 'ID',
      field: 'id',
      type: 'number',
      sortable: true,
      width: 80,
    },
    {
      title: '用户名',
      field: 'username',
      type: 'text',
      searchable: true,
      filterable: true,
      width: 120,
    },
    {
      title: '邮箱',
      field: 'email',
      type: 'text',
      searchable: true,
      filterable: true,
      width: 200,
    },
    {
      title: '状态',
      field: 'status',
      type: 'enum',
      filterable: true,
      enumOptions: [
        { label: '激活', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '待激活', value: 'pending' },
      ],
      width: 100,
    },
    {
      title: '角色',
      field: 'role',
      type: 'text',
      filterable: true,
      width: 100,
    },
    {
      title: '创建时间',
      field: 'createdAt',
      type: 'date',
      sortable: true,
      width: 160,
    },
  ],
  api: {
    endpoint: '/api/users',
    method: 'GET',
    dataPath: 'data',
    totalCountPath: 'total',
    paginationMapping: {
      pageField: 'page',
      pageSizeField: 'limit',
      pageStartsFrom: 1,
    },
    searchMapping: {
      field: 'search',
      operator: 'contains',
    },
    sortMapping: {
      fieldParam: 'sortBy',
      directionParam: 'sortOrder',
      ascValue: 'asc',
      descValue: 'desc',
    },
  },
  search: {
    enabled: true,
    placeholder: '搜索用户名或邮箱',
    debounceMs: 500,
  },
  pagination: {
    enabled: true,
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    showFirstLastButtons: true,
  },
  sort: {
    defaultField: 'createdAt',
    defaultDirection: 'desc',
    multiSort: false,
  },
  options: {
    doubleClickEnabled: true,
    emptyMessage: '没有找到用户',
    loadingType: 'linear',
    density: 'default',
    selectable: true,
  },
};

// 自定义渲染器，内置好常用的渲染器
const customRenderers: CustomRenderer[] = [
  {
    name: 'statusRenderer',
    render: (value: boolean) => (
      <span style={{ color: value ? 'green' : 'red' }}>
        {value ? '成功' : '失败'}
      </span>
    ),
  },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

export const DynamicTableDemo = () => {
  const classes = useStyles();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <div className={classes.container}>
      <Card>
        <CardHeader title="动态表格演示" />
        <CardContent>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="短信记录示例" />
            <Tab label="用户管理示例" />
          </Tabs>

          <div className={classes.tabContent}>
            <TabPanel value={tabValue} index={0}>
              <DynamicTable
                config={smsTableConfig}
                customRenderers={customRenderers}
              />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                用户管理表格（带有单独header的表格）
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                带有说明的表格，这是一个用户管理表格的示例配置，展示了不同的列类型、枚举值渲染等功能。
              </Typography>
              <DynamicTable
                config={userTableConfig}
                autoLoad={false} // 示例数据，不自动加载
              />
            </TabPanel>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
