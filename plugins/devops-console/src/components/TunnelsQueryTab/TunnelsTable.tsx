import { useState, useRef, useMemo } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { Typography, IconButton, Tooltip } from '@material-ui/core';
import RefreshIcon from '@material-ui/icons/Refresh';

import { TunnelRecord } from '../../api';
import { JsonViewerDialog } from '../JsonViewer';

const useStyles = makeStyles({
  refreshButton: {
    marginLeft: 'auto',
  },
});

type TunnelsTableProps = {
  tunnelRecords: TunnelRecord[];
  loading: boolean;
  error?: Error;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  initialSearchText?: string;
};

export const TunnelsTable = ({
  tunnelRecords,
  loading,
  error,
  onSearch,
  onRefresh,
  initialSearchText = '',
}: TunnelsTableProps) => {
  const classes = useStyles();

  // 用于延迟搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于存储选中行的数据和对话框状态
  const [selectedRow, setSelectedRow] = useState<TunnelRecord | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理搜索输入变化，使用防抖
  const handleSearchChange = (searchText: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，延迟1秒触发搜索
    if (onSearch) {
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(searchText);
      }, 1000);
    }
  };

  // 处理行双击事件
  const handleRowClick = (event: any, rowData: TunnelRecord) => {
    setSelectedRow(rowData);
    setDialogOpen(true);
  };

  // 处理对话框关闭
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRow(null);
  };

  // 动态生成列配置
  const columns = useMemo((): TableColumn[] => {
    if (tunnelRecords.length === 0) {
      // 如果没有数据，返回基础列配置
      return [
        { title: '集群', field: 'cluster' },
        { title: '命名空间', field: 'namespace' },
        { title: '应用名称', field: 'app' },
        { title: '隧道名称', field: 'tunnelName' },
        { title: '方向', field: 'direction' },
        { title: '内部地址', field: 'innerAddress' },
        { title: '外部地址', field: 'outerAddress' },
        { title: '描述', field: 'description' },
      ];
    }

    // 基于第一条记录的字段动态生成列
    const firstRecord = tunnelRecords[0];
    const dynamicColumns: TableColumn[] = [];

    Object.keys(firstRecord).forEach(key => {
      // 跳过一些不需要显示的字段
      if (key === '__typename' || key.startsWith('_')) {
        return;
      }

      let title = key;
      let render: ((value: any) => React.ReactNode) | undefined;

      // 根据字段名设置中文标题和特殊渲染
      switch (key) {
        case 'cluster':
          title = '集群';
          break;
        case 'namespace':
          title = '命名空间';
          break;
        case 'app':
          title = '应用名称';
          break;
        case 'port':
          title = '端口';
          break;
        case 'nodePort':
          title = '节点端口';
          break;
        case 'innerAddress':
          title = '内部地址';
          break;
        case 'outerAddress':
          title = '外部地址';
          break;
        case 'direction':
          title = '方向';
          render = (value: any) => (
            <span style={{
              color: value === 'inbound' || value === '入站' ? 'blue' :
                value === 'outbound' || value === '出站' ? 'orange' :
                  'inherit'
            }}>
              {value}
            </span>
          );
          break;
        case 'tunnelName':
          title = '隧道名称';
          break;
        case 'description':
          title = '描述';
          break;
        case 'local':
          title = '本地地址';
          break;
        case 'tunnel':
          title = '隧道地址';
          break;
        case 'target':
          title = '目标地址';
          break;
        case 'targetAddress':
          title = '目标地址详情';
          break;
        case 'proxy':
          title = '代理配置';
          break;
        case 'connectTimeout':
          title = '连接超时';
          render = (value: any) => {
            if (!value) return '-';
            return typeof value === 'number' ? `${value}ms` : value;
          };
          break;
        case 'readTimeout':
          title = '读取超时';
          render = (value: any) => {
            if (!value) return '-';
            return typeof value === 'number' ? `${value}ms` : value;
          };
          break;
        case 'connectionLostTimeout':
          title = '连接丢失超时';
          render = (value: any) => {
            if (!value) return '-';
            return typeof value === 'number' ? `${value}ms` : value;
          };
          break;
        case 'readBytesPerSecond':
          title = '读取速率';
          render = (value: any) => {
            if (!value) return '-';
            return `${value} B/s`;
          };
          break;
        case 'writeBytesPerSecond':
          title = '写入速率';
          render = (value: any) => {
            if (!value) return '-';
            return `${value} B/s`;
          };
          break;
        default:
          // 对于长文本进行截断处理
          render = (value: any) => {
            if (value === null || value === undefined) return '-';
            const strValue = value.toString();
            if (strValue.length > 50) {
              return (
                <Tooltip title={strValue}>
                  <span>{strValue.substring(0, 50)}...</span>
                </Tooltip>
              );
            }
            return strValue;
          };
      }

      dynamicColumns.push({
        title,
        field: key,
        render,
      });
    });

    return dynamicColumns;
  }, [tunnelRecords]);

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return (
    <>
      <Table
        title="盾山配置查询"
        isLoading={loading}
        options={{
          search: true,
          paging: true,
          pageSize: 20,
          pageSizeOptions: [10, 20, 50, 100],
          padding: 'dense',
          showFirstLastPageButtons: true,
          searchText: initialSearchText,
          searchAutoFocus: true,
          loadingType: 'linear',
        }}
        localization={{
          toolbar: {
            searchPlaceholder: '全字段搜索...',
          },
        }}
        data={tunnelRecords}
        columns={columns}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            没有找到匹配的记录
          </Typography>
        }
        actions={[
          {
            icon: () => (
              <Tooltip title="刷新数据">
                <IconButton size="small" onClick={onRefresh}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            ),
            tooltip: '刷新',
            isFreeAction: true,
          },
        ]}
      />

      {/* 详情对话框 */}
      <JsonViewerDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        title="盾山配置 - 详细信息"
        data={selectedRow}
        maxWidth="md"
      />
    </>
  );
};
