import { useState, useRef, useMemo } from 'react';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { Typography, IconButton, Tooltip } from '@material-ui/core';
import RefreshIcon from '@material-ui/icons/Refresh';

import { TunnelRecord } from '../../api';
import { JsonViewerDialog } from '../JsonViewer';

type TunnelsTableProps = {
  tunnelRecords: TunnelRecord[];
  loading: boolean;
  error?: Error;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  initialSearchText?: string;
};

export const TunnelsTable = ({
  tunnelRecords,
  loading,
  error,
  onSearch,
  onRefresh,
  initialSearchText = '',
}: TunnelsTableProps) => {
  // 用于延迟搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于存储选中行的数据和对话框状态
  const [selectedRow, setSelectedRow] = useState<TunnelRecord | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理搜索输入变化，使用防抖
  const handleSearchChange = (searchText: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，延迟1秒触发搜索
    if (onSearch) {
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(searchText);
      }, 1000);
    }
  };

  // 处理行双击事件
  const handleRowClick = (_event?: any, rowData?: TunnelRecord) => {
    if (rowData) {
      setSelectedRow(rowData);
      setDialogOpen(true);
    }
  };

  // 处理对话框关闭
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRow(null);
  };

  // 固定列配置
  const columns: TableColumn[] = [
    {
      title: '集群',
      field: 'cluster',
      render: (value: any) => value || '-'
    },
    {
      title: '命名空间',
      field: 'namespace',
      render: (value: any) => value || '-'
    },
    {
      title: '应用名称',
      field: 'app',
      render: (value: any) => value || '-'
    },
    {
      title: '隧道名称',
      field: 'tunnelName',
      render: (value: any) => value || '-'
    },
    {
      title: '方向',
      field: 'direction',
      render: (value: any) => {
        if (!value) return '-';
        return (
          <span style={{
            color: value === 'inbound' || value === '入站' ? 'blue' :
              value === 'outbound' || value === '出站' ? 'orange' :
                'inherit'
          }}>
            {value}
          </span>
        );
      }
    },
    {
      title: '端口',
      field: 'port',
      render: (value: any) => value || '-'
    },
    {
      title: '节点端口',
      field: 'nodePort',
      render: (value: any) => value || '-'
    },
    {
      title: '内部地址',
      field: 'innerAddress',
      render: (value: any) => {
        if (!value) return '-';
        const strValue = value.toString();
        if (strValue.length > 30) {
          return (
            <Tooltip title={strValue}>
              <span>{strValue.substring(0, 30)}...</span>
            </Tooltip>
          );
        }
        return strValue;
      }
    },
    {
      title: '外部地址',
      field: 'outerAddress',
      render: (value: any) => {
        if (!value) return '-';
        const strValue = value.toString();
        if (strValue.length > 30) {
          return (
            <Tooltip title={strValue}>
              <span>{strValue.substring(0, 30)}...</span>
            </Tooltip>
          );
        }
        return strValue;
      }
    },
    {
      title: '本地地址',
      field: 'local',
      render: (value: any) => {
        if (!value) return '-';
        const strValue = value.toString();
        if (strValue.length > 25) {
          return (
            <Tooltip title={strValue}>
              <span>{strValue.substring(0, 25)}...</span>
            </Tooltip>
          );
        }
        return strValue;
      }
    },
    {
      title: '目标地址',
      field: 'target',
      render: (value: any) => {
        if (!value) return '-';
        const strValue = value.toString();
        if (strValue.length > 25) {
          return (
            <Tooltip title={strValue}>
              <span>{strValue.substring(0, 25)}...</span>
            </Tooltip>
          );
        }
        return strValue;
      }
    },
    {
      title: '描述',
      field: 'description',
      render: (value: any) => {
        if (!value) return '-';
        const strValue = value.toString();
        if (strValue.length > 40) {
          return (
            <Tooltip title={strValue}>
              <span>{strValue.substring(0, 40)}...</span>
            </Tooltip>
          );
        }
        return strValue;
      }
    },
  ];

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return (
    <>
      <Table
        title="盾山配置查询"
        isLoading={loading}
        options={{
          search: true,
          paging: true,
          pageSize: 20,
          pageSizeOptions: [10, 20, 50, 100],
          padding: 'dense',
          showFirstLastPageButtons: true,
          searchText: initialSearchText,
          searchAutoFocus: true,
          loadingType: 'linear',
        }}
        localization={{
          toolbar: {
            searchPlaceholder: '全字段搜索...',
          },
        }}
        data={tunnelRecords}
        columns={columns}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            没有找到匹配的记录
          </Typography>
        }
        actions={[
          {
            icon: () => (
              <Tooltip title="刷新数据">
                <IconButton size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            ),
            tooltip: '刷新',
            isFreeAction: true,
            onClick: () => onRefresh && onRefresh(),
          },
        ]}
      />

      {/* 详情对话框 */}
      <JsonViewerDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        title="盾山配置 - 详细信息"
        data={selectedRow}
        maxWidth="md"
      />
    </>
  );
};
