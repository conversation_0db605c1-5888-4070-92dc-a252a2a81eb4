import { useState, useEffect } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { TunnelsTable } from './TunnelsTable';
import { tunnelsApiRef, TunnelRecord } from '../../api';

export const TunnelsQueryTab = () => {
  const tunnelsApi = useApi(tunnelsApiRef);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);
  const [tunnelRecords, setTunnelRecords] = useState<TunnelRecord[]>([]);
  const [allRecords, setAllRecords] = useState<TunnelRecord[]>([]); // 存储所有数据用于前端分页和过滤
  const [searchQuery, setSearchQuery] = useState('');

  // 加载数据
  const loadTunnels = async () => {
    setLoading(true);
    setError(undefined);

    try {
      const records = await tunnelsApi.getTunnels();
      setAllRecords(records);
      setTunnelRecords(records);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadTunnels();
  }, []);

  // 处理搜索
  const handleSearch = (searchText: string) => {
    setSearchQuery(searchText);

    if (!searchText.trim()) {
      setTunnelRecords(allRecords);
      return;
    }

    // 全字段搜索
    const filteredRecords = allRecords.filter(record => {
      return Object.values(record).some(value => {
        if (value === null || value === undefined) return false;
        return value.toString().toLowerCase().includes(searchText.toLowerCase());
      });
    });

    setTunnelRecords(filteredRecords);
  };

  // 处理刷新
  const handleRefresh = () => {
    setSearchQuery('');
    loadTunnels();
  };

  return (
    <TunnelsTable
      tunnelRecords={tunnelRecords}
      loading={loading}
      error={error}
      onSearch={handleSearch}
      onRefresh={handleRefresh}
      initialSearchText={searchQuery}
    />
  );
};
