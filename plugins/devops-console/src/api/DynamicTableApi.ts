import {
  createApiRef,
  <PERSON><PERSON><PERSON>,
  Fetch<PERSON><PERSON>,
} from '@backstage/core-plugin-api';
import {
  ApiConfig,
  DynamicTableQueryParams,
  DynamicTableResponse,
  DynamicTableConfig,
} from '../types/DynamicTable';

export const dynamicTableApiRef = createApiRef<DynamicTableApi>({
  id: 'plugin.devops-console.dynamic-table',
});

export interface DynamicTableApi {
  /** 查询表格数据 */
  queryTableData(
    config: ApiConfig,
    params: DynamicTableQueryParams
  ): Promise<DynamicTableResponse>;

  /** 保存表格配置 */
  saveTableConfig(config: DynamicTableConfig): Promise<void>;

  /** 获取表格配置 */
  getTableConfig(id: string): Promise<DynamicTableConfig>;

  /** 获取所有表格配置 */
  getAllTableConfigs(): Promise<DynamicTableConfig[]>;

  /** 删除表格配置 */
  deleteTableConfig(id: string): Promise<void>;
}

export class DynamicTableApiClient implements DynamicTableApi {
  constructor(
    private readonly discoveryApi: DiscoveryApi,
    private readonly fetchApi: FetchApi,
  ) { }

  async queryTableData(
    config: ApiConfig,
    params: DynamicTableQueryParams
  ): Promise<DynamicTableResponse> {
    try {
      // 构建请求URL
      let url = config.endpoint;

      // 如果是GET请求，将参数添加到URL
      if (config.method === 'GET') {
        const searchParams = new URLSearchParams();

        // 添加分页参数
        if (config.paginationMapping && params.page !== undefined) {
          const pageStartsFrom = config.paginationMapping.pageStartsFrom || 1;
          const pageValue = pageStartsFrom === 0 ? params.page - 1 : params.page;
          searchParams.append(config.paginationMapping.pageField, pageValue.toString());
        }
        if (config.paginationMapping && params.pageSize !== undefined) {
          searchParams.append(config.paginationMapping.pageSizeField, params.pageSize.toString());
        }

        // 添加搜索参数
        if (config.searchMapping && params.search) {
          searchParams.append(config.searchMapping.field, params.search);
        }

        // 添加排序参数
        if (config.sortMapping && params.sortField) {
          searchParams.append(config.sortMapping.fieldParam, params.sortField);
          if (params.sortDirection) {
            const directionValue = params.sortDirection === 'asc'
              ? (config.sortMapping.ascValue || 'asc')
              : (config.sortMapping.descValue || 'desc');
            searchParams.append(config.sortMapping.directionParam, directionValue);
          }
        }

        if (searchParams.toString()) {
          url += (url.includes('?') ? '&' : '?') + searchParams.toString();
        }
      }

      // 构建请求选项
      const requestOptions: RequestInit = {
        method: config.method,
        headers: {
          'Content-Type': 'application/json',
          ...config.headers,
        },
      };

      // 如果是POST/PUT/DELETE请求，构建请求体
      if (config.method !== 'GET') {
        const body = { ...config.bodyTemplate };

        // 添加分页参数到请求体
        if (config.paginationMapping && params.page !== undefined) {
          const pageStartsFrom = config.paginationMapping.pageStartsFrom || 1;
          const pageValue = pageStartsFrom === 0 ? params.page - 1 : params.page;
          body[config.paginationMapping.pageField] = pageValue;
        }
        if (config.paginationMapping && params.pageSize !== undefined) {
          body[config.paginationMapping.pageSizeField] = params.pageSize;
        }

        // 添加搜索参数到请求体
        if (config.searchMapping && params.search) {
          body[config.searchMapping.field] = params.search;
        }

        // 添加排序参数到请求体
        if (config.sortMapping && params.sortField) {
          body[config.sortMapping.fieldParam] = params.sortField;
          if (params.sortDirection) {
            const directionValue = params.sortDirection === 'asc'
              ? (config.sortMapping.ascValue || 'asc')
              : (config.sortMapping.descValue || 'desc');
            body[config.sortMapping.directionParam] = directionValue;
          }
        }

        // 添加其他过滤参数
        if (params.filters) {
          Object.assign(body, params.filters);
        }

        requestOptions.body = JSON.stringify(body);
      }
      // 发送请求，必须经过proxy代理
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const finalUrl = `${proxyUrl}${url.startsWith('/') ? '' : '/'}${url}`;

      const response = await this.fetchApi.fetch(finalUrl, requestOptions);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();

      // 根据配置提取数据
      const data = this.extractDataFromResponse(responseData, config.dataPath || 'data');
      const total = this.extractDataFromResponse(responseData, config.totalCountPath || 'total');

      const totalCount = typeof total === 'number'
        ? total
        : Array.isArray(data) ? data.length : 0;

      return {
        data: Array.isArray(data) ? data : [],
        total: totalCount,
        page: params.page,
        pageSize: params.pageSize,
      };
    } catch (error) {
      throw error;
    }
  }

  private extractDataFromResponse(response: any, path: string): any {
    if (!path) return response;

    const keys = path.split('.');
    let current = response;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }

    return current;
  }

  async saveTableConfig(config: DynamicTableConfig): Promise<void> {
    try {
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const apiUrl = `${proxyUrl}/devops-console/api/table-configs`;

      const response = await this.fetchApi.fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`Failed to save table config: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      throw error;
    }
  }

  async getTableConfig(id: string): Promise<DynamicTableConfig> {
    try {
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const apiUrl = `${proxyUrl}/devops-console/api/table-configs/${id}`;

      const response = await this.fetchApi.fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get table config: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      throw error;
    }
  }

  async getAllTableConfigs(): Promise<DynamicTableConfig[]> {
    try {
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const apiUrl = `${proxyUrl}/devops-console/api/table-configs`;

      const response = await this.fetchApi.fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get table configs: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      throw error;
    }
  }

  async deleteTableConfig(id: string): Promise<void> {
    try {
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const apiUrl = `${proxyUrl}/devops-console/api/table-configs/${id}`;

      const response = await this.fetchApi.fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete table config: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      throw error;
    }
  }
}

export const createDynamicTableApiClient = (options: {
  discoveryApi: DiscoveryApi;
  fetchApi: FetchApi;
}): DynamicTableApi => {
  return new DynamicTableApiClient(options.discoveryApi, options.fetchApi);
};
