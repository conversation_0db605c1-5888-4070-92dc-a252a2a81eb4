import {
  createApiRef,
  <PERSON><PERSON><PERSON>,
  Fetch<PERSON><PERSON>,
} from '@backstage/core-plugin-api';

export interface SmsRecord {
  msgId: string;
  batchMsgId: string;
  serialId: string;
  phone: string;
  content: string;
  sign: string;
  providerId: string;
  providerName: string;
  sendTime: string;
  bizName: string;
  status: boolean;
  smsLength: number;
  smsSize: number;
  smsType: string;
  international: boolean;
}

export interface SmsQueryParams {
  pageNum: number;
  pageSize: number;
  phone?: string; // 用于手机号码搜索
}

export interface SmsQueryResponse {
  results: SmsRecord[];
  count: number;
  pageNum: number;
  pageSize: number;
  totalPage: number;
  totalCount: number;
}

export const smsApiRef = createApiRef<SmsApi>({
  id: 'plugin.devops-console.sms',
});

export interface SmsApi {
  querySmsRecords(params: SmsQueryParams): Promise<SmsQueryResponse>;
}

/**
 * API 工厂函数，用于创建 SmsApiClient 实例
 */
export const createSmsApiClient = (options: {
  discoveryApi: DiscoveryApi;
  fetchApi: FetchApi;
}): SmsApi => {
  return new SmsApiClient(options.discoveryApi, options.fetchApi);
};

export class SmsApiClient implements SmsApi {
  private readonly apiPath = '/api/v2/sms/histories';

  constructor(
    private readonly discoveryApi: DiscoveryApi,
    private readonly fetchApi: FetchApi,
  ) { }

  async querySmsRecords(params: SmsQueryParams): Promise<SmsQueryResponse> {
    try {
      const requestBody: Record<string, any> = {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        phone: params.phone,
      };

      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const apiUrl = `${proxyUrl}/egress-api-service${this.apiPath}`;

      const response = await this.fetchApi.fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = response.ok ? await response.json() : {};

      return {
        results: data.results || [],
        count: data.count || 0,
        pageNum: data.pageNum || 1,
        pageSize: data.pageSize || 10,
        totalPage: data.totalPage || 1,
        totalCount: data.totalCount || 0,
      };
    } catch (error) {
      throw error;
    }
  }
}
