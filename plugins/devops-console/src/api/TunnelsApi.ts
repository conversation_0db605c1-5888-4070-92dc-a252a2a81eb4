import {
  createApiRef,
  <PERSON><PERSON><PERSON>,
  <PERSON>tch<PERSON><PERSON>,
} from '@backstage/core-plugin-api';

export const tunnelsApiRef = createApiRef<TunnelsApi>({
  id: 'plugin.devops-console.tunnels',
});

// 盾山配置记录类型定义
export interface TunnelRecord {
  // 基础字段，稍后根据实际API返回值补充
  id?: string;
  name?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  // 更多字段待补充...
  [key: string]: any; // 允许动态字段
}

// 查询参数
export interface TunnelsQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  filters?: Record<string, any>;
}

// API响应
export interface TunnelsResponse {
  data: TunnelRecord[];
  total?: number;
  page?: number;
  pageSize?: number;
}

export interface TunnelsApi {
  /** 获取盾山配置列表 */
  getTunnels(params?: TunnelsQueryParams): Promise<TunnelRecord[]>;
}

export class TunnelsApiClient implements TunnelsApi {
  constructor(
    private readonly discoveryApi: DiscoveryApi,
    private readonly fetchApi: FetchApi,
  ) {}

  async getTunnels(params?: TunnelsQueryParams): Promise<TunnelRecord[]> {
    try {
      // 构建查询参数
      const searchParams = new URLSearchParams();
      
      if (params?.page) {
        searchParams.append('page', params.page.toString());
      }
      if (params?.pageSize) {
        searchParams.append('pageSize', params.pageSize.toString());
      }
      if (params?.search) {
        searchParams.append('search', params.search);
      }
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            searchParams.append(key, value.toString());
          }
        });
      }

      // 构建请求URL
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const queryString = searchParams.toString();
      const url = `${proxyUrl}/tunnels${queryString ? `?${queryString}` : ''}`;

      const response = await this.fetchApi.fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // 假设API返回的是一个数组，如果是包装对象需要调整
      return Array.isArray(data) ? data : data.data || [];
    } catch (error) {
      throw new Error(`Failed to fetch tunnels: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

export const createTunnelsApiClient = (options: {
  discoveryApi: DiscoveryApi;
  fetchApi: FetchApi;
}): TunnelsApi => {
  return new TunnelsApiClient(options.discoveryApi, options.fetchApi);
};
