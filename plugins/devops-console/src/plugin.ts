import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  discoveryApiRef,
  fetchApiRef,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';
import {
  smsApiRef,
  createSmsApiClient,
  dynamicTableApiRef,
  createDynamicTableApiClient,
} from './api';

export const devopsConsolePlugin = createPlugin({
  id: 'devops-console',
  routes: {
    root: rootRouteRef,
  },
  apis: [
    createApiFactory({
      api: smsApiRef,
      deps: { discoveryApi: discoveryApiRef, fetchApi: fetchApiRef },
      factory: ({ discoveryApi, fetchApi }) => createSmsApiClient({ discoveryApi, fetchApi }),
    }),
    createApiFactory({
      api: dynamicTableApiRef,
      deps: { discoveryApi: discoveryApiRef, fetchApi: fetchApiRef },
      factory: ({ discoveryApi, fetchApi }) => createDynamicTableApiClient({ discoveryApi, fetchApi }),
    }),
  ],
});

export const DevopsConsolePage = devopsConsolePlugin.provide(
  createRoutableExtension({
    name: 'DevopsConsolePage',
    component: () =>
      import('./components/DevopsConsoleComponent').then(m => m.DevopsConsoleComponent),
    mountPoint: rootRouteRef,
  }),
);
