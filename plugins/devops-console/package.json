{"name": "@internal/plugin-devops-console", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.esm.js", "types": "dist/index.d.ts"}, "backstage": {"role": "frontend-plugin", "pluginId": "devops-console", "pluginPackages": ["@internal/plugin-devops-console", "@internal/plugin-devops-console-common", "@internal/plugin-devops-console-backend"]}, "sideEffects": false, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/core-components": "^0.17.3", "@backstage/core-plugin-api": "^1.10.8", "@backstage/plugin-permission-react": "^0.4.35", "@backstage/plugin-user-settings": "^0.8.23", "@backstage/theme": "^0.6.6", "@date-io/date-fns": "^1.3.13", "@internal/plugin-devops-console-common": "workspace:^", "@material-ui/core": "^4.9.13", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "^3.2.10", "date-fns": "^2.16.1", "react-use": "^17.2.4"}, "peerDependencies": {"react": "^16.13.1 || ^17.0.0 || ^18.0.0"}, "devDependencies": {"@backstage/cli": "^0.33.0", "@backstage/core-app-api": "^1.17.1", "@backstage/dev-utils": "^1.1.11", "@backstage/test-utils": "^1.7.9", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "msw": "^1.0.0", "react": "^16.13.1 || ^17.0.0 || ^18.0.0"}, "files": ["dist"]}