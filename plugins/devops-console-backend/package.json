{"name": "@internal/plugin-devops-console-backend", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin", "pluginId": "devops-console", "pluginPackages": ["@internal/plugin-devops-console", "@internal/plugin-devops-console-common", "@internal/plugin-devops-console-backend"]}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-defaults": "^0.11.0", "@backstage/backend-plugin-api": "^1.4.0", "@backstage/catalog-client": "^1.10.1", "@backstage/errors": "^1.2.7", "@backstage/plugin-catalog-node": "^1.17.1", "@backstage/plugin-permission-common": "^0.9.0", "@backstage/plugin-permission-node": "^0.10.1", "@internal/plugin-devops-console-common": "workspace:^", "express": "^4.17.1", "express-promise-router": "^4.1.0", "zod": "^3.22.4"}, "devDependencies": {"@backstage/backend-test-utils": "^1.6.0", "@backstage/cli": "^0.33.0", "@types/express": "^4.17.6", "@types/supertest": "^2.0.12", "supertest": "^6.2.4"}, "files": ["dist"]}