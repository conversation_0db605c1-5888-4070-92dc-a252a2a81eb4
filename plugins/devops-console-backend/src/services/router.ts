import express from 'express';
import Router from 'express-promise-router';
import { HttpAuthService, LoggerService } from '@backstage/backend-plugin-api';

/**
 * Dependencies of the todo-list router
 */
export interface RouterOptions {
  logger: LoggerService;
  httpAuth: HttpAuthService;
}

export async function createRouter(
  _options: RouterOptions,
): Promise<express.Router> {
  const router = Router();
  router.use(express.json());

  router.get('/health', (_, response) => {
    response.json({ status: 'ok' });
  });

  return router;
}
