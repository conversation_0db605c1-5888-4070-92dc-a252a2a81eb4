import {
  coreServices,
  createBackendPlugin,
} from '@backstage/backend-plugin-api';
import { catalogServiceRef } from '@backstage/plugin-catalog-node';
import { devOpsPermissions } from '@internal/plugin-devops-console-common';
import { createRouter } from './services/router';

/**
 * devopsConsolePlugin backend plugin
 *
 * @public
 */
export const devopsConsolePlugin = createBackendPlugin({
  pluginId: 'devops-console',
  register(env) {
    env.registerInit({
      deps: {
        logger: coreServices.logger,
        httpAuth: coreServices.httpAuth,
        httpRouter: coreServices.httpRouter,
        catalog: catalogServiceRef,
        permissions: coreServices.permissions,
        permissionsRegistry: coreServices.permissionsRegistry,
      },
      async init({ logger, httpAuth, httpRouter, permissionsRegistry }) {
        permissionsRegistry.addPermissions(devOpsPermissions);

        httpRouter.use(
          await createRouter({
            logger,
            httpAuth,
          }),
        );
        httpRouter.addAuthPolicy({
          path: '/health',
          allow: 'unauthenticated',
        });
      },
    });
  },
});
