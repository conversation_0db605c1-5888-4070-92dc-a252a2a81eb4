stages:
  - deploy
variables:
  DEPLOY_APP_NAME: "fs-idp-backstage"
  DEPLOY_APP_DOCKER_TAG: "${CI_COMMIT_REF_NAME}"
  DEPLOY_APP_DOCKER_IMAGE: "reg.firstshare.cn/app/fs-idp-backstage:${CI_COMMIT_REF_NAME}"
  DEPLOY_APP_DOCKER_IMAGE_FONESHARE: "reg.foneshare.cn/app/fs-idp-backstage:${CI_COMMIT_REF_NAME}"
  FS_PROXY: "http://slpn.firstshare.cn:8080"
  NO_PROXY: "localhost,127.0.0.1,10.*,172.*,registry-npm.firstshare.cn"

.deploy_template:
  stage: deploy
  tags:
    - k8s-deploy
  image: reg.firstshare.cn/base/fs-kubectl:v3.0

build_image:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG
    - when: manual
  tags:
    - docker-build
  script:
    # - docker pull ${DEPLOY_APP_DOCKER_IMAGE} || true
    # - docker build --pull --build-arg http_proxy=${FS_PROXY} --build-arg https_proxy=${FS_PROXY} -t ${DEPLOY_APP_DOCKER_IMAGE} .
    - docker build -t ${DEPLOY_APP_DOCKER_IMAGE} .
    - docker push ${DEPLOY_APP_DOCKER_IMAGE}
    - docker tag ${DEPLOY_APP_DOCKER_IMAGE} ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}
    - docker push ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}


firstshare:
  extends: .deploy_template
  stage: deploy
  when: manual
  variables:
    DEPLOY_K8S_CLUSTER: "firstshare-k8s1"
    DEPLOY_K8S_NAMESPACE: "devops"
    DEPLOY_K8S_VALUES_FILE: "values-firstshare-k8s1.yaml"
    CHART_VERSION: "2.4.0"
  script:
    - helm upgrade fs-idp-backstage --install -n ${DEPLOY_K8S_NAMESPACE} --set backstage.image.tag=${DEPLOY_APP_DOCKER_TAG} --kubeconfig=/kube/kubeconf/${DEPLOY_K8S_CLUSTER} --history-max=10 --debug --wait --timeout=10m oci://reg.firstshare.cn/chartrepo/backstage --version ${CHART_VERSION} -f k8s-manifest/charts/${DEPLOY_K8S_VALUES_FILE}

foneshare:
  extends: .deploy_template
  stage: deploy
  when: manual
  tags:
    - k8s-deploy-foneshare
  variables:
    DEPLOY_K8S_CLUSTER: "foneshare-k8s0"
    DEPLOY_K8S_NAMESPACE: "devops"
    DEPLOY_K8S_VALUES_FILE: "values-foneshare-k8s0.yaml"
    CHART_VERSION: "2.4.0"
  script:
    - helm upgrade fs-idp-backstage --install -n ${DEPLOY_K8S_NAMESPACE} --set backstage.image.tag=${DEPLOY_APP_DOCKER_TAG} --kubeconfig=/kube/kubeconf/${DEPLOY_K8S_CLUSTER} --history-max=10 --debug --wait --timeout=10m oci://reg.firstshare.cn/chartrepo/backstage --version ${CHART_VERSION} -f k8s-manifest/charts/${DEPLOY_K8S_VALUES_FILE}




