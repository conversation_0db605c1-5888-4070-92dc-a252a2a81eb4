---
# https://backstage.io/docs/features/software-catalog/descriptor-format#contents
apiVersion: backstage.io/v1alpha1
kind: Domain
metadata:
  name: fs-devops-all
  title: 平台工程
  description: 纷享人的平台工程，包括内部开发者平台、运维平台等一系列面向开发者的应用
  tags:
    - devops
spec:
  owner: group:default/6d5893e0-499a-460d-979a-37456327d10d
  type: product-group
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: fs-idp
  title: 内部开发者平台
  description: 内部开发者平台
  tags:
    - devops
  links:
    - url: https://dash.firstshare.cn
      title: 内部开发者平台前端
      icon: dashboard
    - url: https://git.firstshare.cn/devops/fs-idp-helper
      title: 内部开发者平台后端
      icon: github
spec:
  owner: group:default/6d5893e0-499a-460d-979a-37456327d10d
  domain: default/fs-devops-all
---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: fs-idp-backstage
  description: 内部开发者平台前端
  annotations:
    # 'backstage.io/kubernetes-id': k8s1
    # 'backstage.io/kubernetes-cluster': k8s1
    'backstage.io/kubernetes-label-selector': 'app.kubernetes.io/name=backstage'
  tags:
    - react
  links:
    - url: https://dash.firstshare.cn
      title: 内部开发者平台前端
      icon: dashboard
    - url: https://git.firstshare.cn/devops/fs-idp-backstage
      title: 前端代码
      icon: github
spec:
  type: website
  lifecycle: experimental
  owner: group:default/6d5893e0-499a-460d-979a-37456327d10d
  system: fs-idp
  dependsOn:
    - component:default/fs-idp-backend
    - resource:default/pg-172-16-10-135
---

apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: fs-idp-backend
  description: 内部开发者平台后端
  annotations:
    # 'backstage.io/kubernetes-id': k8s1
    # 'backstage.io/kubernetes-cluster': k8s1
    'backstage.io/kubernetes-label-selector': 'app=fs-idp-helper'
  tags:
    - java
  links:
    - url: https://git.firstshare.cn/devops/fs-idp-helper
      title: 内部开发者平台后端
      icon: github
spec:
  type: service
  lifecycle: experimental
  owner: group:default/6d5893e0-499a-460d-979a-37456327d10d
  system: fs-idp
  dependsOn:
    - resource:default/pg-172-16-10-135

---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: pg-172-16-10-135
  description: 内部开发者平台数据库
  annotations:
    fxiaoke.com/host: '*************'
    fxiaoke.com/port: '5432'
    fxiaoke.com/database: 'backstage'
  tags:
    - postgresql
spec:
  type: database
  lifecycle: production
  owner: group:default/6d5893e0-499a-460d-979a-37456327d10d
  system: fs-idp