import {
  CatalogKindExploreContent,
  ExploreLayout,
} from '@backstage-community/plugin-explore';


export const ExplorePage = () => {
  return (
    <ExploreLayout
      title="纷享服务一览表"
      subtitle=""
    >
      {/* <ExploreLayout.Route path="domains" title="Domains">
        <CatalogKindExploreContent kind="domain" />
      </ExploreLayout.Route> */}
      <ExploreLayout.Route path="systems" title="Systems">
        <CatalogKindExploreContent kind="system" />
      </ExploreLayout.Route>
      {/* <ExploreLayout.Route path="tools" title="Tools">
        <ToolExplorerContent />
      </ExploreLayout.Route> */}
    </ExploreLayout>
  );
};

export const explorePage = <ExplorePage />;