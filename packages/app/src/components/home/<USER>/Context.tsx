import { useState, useEffect, useContext, createContext } from 'react';
import { useApi, storageApiRef } from '@backstage/core-plugin-api';
import type { IframeWidget, IframeWidgetContextValue } from './types';

const Context = createContext<IframeWidgetContextValue | undefined>(undefined);

export const ContextProvider = (props: { children: JSX.Element }) => {
  const { children } = props;
  const storageApi = useApi(storageApiRef);
  const widgetStore = storageApi.forBucket('user-iframe-widget');

  const [widget, setWidget] = useState<IframeWidget | null>(null);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [height, setHeight] = useState(400);

  useEffect(() => {
    const subscription = widgetStore.observe$<IframeWidget>('iframe-widget').subscribe({
      next: snapshot => {
        if (snapshot.presence === 'present' && snapshot.value) {
          setWidget(snapshot.value);
        } 
        setLoading(false);
      },
      error: error => {
        console.error('Failed to load iframe widget:', error);
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, [widgetStore]);

  const saveWidget = async (updatedWidget: IframeWidget) => {
    try {
      await widgetStore.set('iframe-widget', updatedWidget);
      setWidget(updatedWidget);
      return true;
    } catch (error) {
      console.error('Failed to save iframe widget:', error);
      return false;
    }
  };

  const handleOpenDialog = () => {
    if (widget) {
      setTitle(widget.title);
      setUrl(widget.url);
      setHeight(widget.height || 400);
    } else {
      setTitle('');
      setUrl('');
      setHeight(400);
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setTitle('');
    setUrl('');
    setHeight(400);
  };

  const handleSaveWidget = async () => {
    if (!title.trim() || !url.trim()) {
      return;
    }

    try {
      const newWidget: IframeWidget = {
        id: 'default',
        title,
        url,
        height,
      };

      const saved = await saveWidget(newWidget);
      if (saved) {
        handleCloseDialog();
      }
    } catch (error) {
      console.error('Failed to save widget:', error);
    }
  };

  const handleClearWidget = async () => {
    try {
      await widgetStore.remove('iframe-widget');
      setWidget(null);
    } catch (error) {
      console.error('Failed to clear widget:', error);
    }
  };

  const value: IframeWidgetContextValue = {
    widget,
    loading,
    dialogOpen,
    title,
    url,
    height,
    setTitle,
    setUrl,
    setHeight,
    handleOpenDialog,
    handleCloseDialog,
    handleSaveWidget,
    handleClearWidget,
  };

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export const useIframeWidget = () => {
  const value = useContext(Context);
  if (value === undefined) {
    throw new Error('useIframeWidget must be used within a IframeWidgetProvider');
  }
  return value;
};

export default Context;
