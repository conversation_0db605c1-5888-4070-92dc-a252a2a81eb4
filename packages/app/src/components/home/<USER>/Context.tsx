import { useState, useEffect, useContext, createContext } from 'react';
import { useApi, storageApiRef } from '@backstage/core-plugin-api';
import type { Shortcut, CustomShortcutsContextValue } from './types';

const Context = createContext<CustomShortcutsContextValue | undefined>(undefined);

export const ContextProvider = (props: { children: JSX.Element }) => {
  const { children } = props;
  const storageApi = useApi(storageApiRef);
  const shortcutsStore = storageApi.forBucket('user-shortcuts');

  const [shortcuts, setShortcuts] = useState<Shortcut[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingShortcut, setEditingShortcut] = useState<Shortcut | null>(null);
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');

  useEffect(() => {
    // 使用 UserSettingsStorage 时，使用 observe 方法监听数据变化
    const subscription = shortcutsStore.observe$<Shortcut[]>('shortcuts').subscribe({
      next: snapshot => {
        if (snapshot.presence === 'present' && snapshot.value) {
          setShortcuts(snapshot.value);
        } else {
          setShortcuts([]);
        }
        setLoading(false);
      },
      error: error => {
        console.error('Failed to load shortcuts:', error);
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, [shortcutsStore]);

  const saveShortcuts = async (updatedShortcuts: Shortcut[]) => {
    try {
      await shortcutsStore.set('shortcuts', updatedShortcuts);
      setShortcuts(updatedShortcuts);
      return true;
    } catch (error) {
      console.error('Failed to save shortcuts:', error);
      return false;
    }
  };

  const handleOpenDialog = (shortcut?: Shortcut) => {
    if (shortcut) {
      setEditingShortcut(shortcut);
      setTitle(shortcut.title);
      setUrl(shortcut.url);
    } else {
      setEditingShortcut(null);
      setTitle('');
      setUrl('');
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingShortcut(null);
    setTitle('');
    setUrl('');
  };

  const handleSaveShortcut = async () => {
    if (!title.trim() || !url.trim()) {
      return;
    }

    try {
      let updatedShortcuts: Shortcut[];

      if (editingShortcut) {
        // Update existing shortcut
        updatedShortcuts = shortcuts.map(s =>
          s.id === editingShortcut.id ? { ...s, title, url } : s
        );
      } else {
        // Add new shortcut
        const newShortcut: Shortcut = {
          id: Date.now().toString(),
          title,
          url,
        };
        updatedShortcuts = [...shortcuts, newShortcut];
      }

      const saved = await saveShortcuts(updatedShortcuts);
      if (saved) {
        handleCloseDialog();
      }
    } catch (error) {
      // URL validation failed
    }
  };

  const handleDeleteShortcut = async (id: string) => {
    const updatedShortcuts = shortcuts.filter(s => s.id !== id);
    await saveShortcuts(updatedShortcuts);
  };

  const handleOpenLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const value: CustomShortcutsContextValue = {
    shortcuts,
    loading,
    dialogOpen,
    editingShortcut,
    title,
    url,
    setTitle,
    setUrl,
    handleOpenDialog,
    handleCloseDialog,
    handleSaveShortcut,
    handleDeleteShortcut,
    handleOpenLink,
  };

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export const useCustomShortcuts = () => {
  const value = useContext(Context);
  if (value === undefined) {
    throw new Error('useCustomShortcuts must be used within a CustomShortcutsProvider');
  }
  return value;
};

export default Context;
