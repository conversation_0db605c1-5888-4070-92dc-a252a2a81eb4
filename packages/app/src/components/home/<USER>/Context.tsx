import { useState, useEffect, useContext, createContext } from 'react';
import { useApi, storageApiRef } from '@backstage/core-plugin-api';
import type { MarkdownNote, MarkdownNotesContextValue } from './types';

const Context = createContext<MarkdownNotesContextValue | undefined>(undefined);

export const ContextProvider = (props: { children: JSX.Element }) => {
  const { children } = props;
  const storageApi = useApi(storageApiRef);
  const notesStore = storageApi.forBucket('user-markdown-notes');

  const [note, setNote] = useState<MarkdownNote | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState('');

  useEffect(() => {
    // 使用 UserSettingsStorage 时，使用 observe 方法监听数据变化
    const subscription = notesStore.observe$<MarkdownNote>('markdown-note').subscribe({
      next: snapshot => {
        if (snapshot.presence === 'present' && snapshot.value) {
          setNote(snapshot.value);
        } else {
          // 如果没有数据，创建默认的空笔记
          const defaultNote: MarkdownNote = {
            id: crypto.randomUUID(),
            content: '# 我的笔记\n\n在这里记录你的想法...',
            updatedAt: new Date().toISOString(),
          };
          setNote(defaultNote);
        }
        setLoading(false);
      },
      error: error => {
        console.error('Failed to load markdown note:', error);
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, [notesStore]);

  const saveNote = async (updatedNote: MarkdownNote) => {
    try {
      await notesStore.set('markdown-note', updatedNote);
      setNote(updatedNote);
      return true;
    } catch (error) {
      console.error('Failed to save markdown note:', error);
      return false;
    }
  };

  const handleStartEdit = () => {
    setEditContent(note?.content || '');
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent('');
  };

  const handleSaveEdit = async () => {
    if (!note) return;

    const updatedNote: MarkdownNote = {
      ...note,
      content: editContent,
      updatedAt: new Date().toISOString(),
    };

    const saved = await saveNote(updatedNote);
    if (saved) {
      setIsEditing(false);
      setEditContent('');
    }
  };

  const toggleEditMode = () => {
    if (isEditing) {
      handleCancelEdit();
    } else {
      handleStartEdit();
    }
  };

  const value: MarkdownNotesContextValue = {
    note,
    loading,
    isEditing,
    editContent,
    setEditContent,
    handleStartEdit,
    handleCancelEdit,
    handleSaveEdit,
    toggleEditMode,
  };

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export const useMarkdownNotes = () => {
  const value = useContext(Context);
  if (value === undefined) {
    throw new Error('useMarkdownNotes must be used within a MarkdownNotesProvider');
  }
  return value;
};

export default Context;
