import type { JsonObject } from '@backstage/types';

export interface Shortcut extends JsonObject {
  id: string;
  title: string;
  url: string;
}

export interface CustomShortcutsContextValue {
  shortcuts: Shortcut[];
  loading: boolean;
  dialogOpen: boolean;
  editingShortcut: Shortcut | null;
  title: string;
  url: string;
  setTitle: (title: string) => void;
  setUrl: (url: string) => void;
  handleOpenDialog: (shortcut?: Shortcut) => void;
  handleCloseDialog: () => void;
  handleSaveShortcut: () => Promise<void>;
  handleDeleteShortcut: (id: string) => Promise<void>;
  handleOpenLink: (url: string) => void;
}
