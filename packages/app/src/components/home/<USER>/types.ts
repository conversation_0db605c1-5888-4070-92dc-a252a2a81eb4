import type { JsonObject } from '@backstage/types';

export interface MarkdownNote extends JsonObject {
  id: string;
  content: string;
  updatedAt: string;
}

export interface MarkdownNotesContextValue {
  note: MarkdownNote | null;
  loading: boolean;
  isEditing: boolean;
  editContent: string;
  setEditContent: (content: string) => void;
  handleStartEdit: () => void;
  handleCancelEdit: () => void;
  handleSaveEdit: () => Promise<void>;
  toggleEditMode: () => void;
}
