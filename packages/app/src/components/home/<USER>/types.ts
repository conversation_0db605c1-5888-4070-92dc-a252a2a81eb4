import type { JsonObject } from '@backstage/types';

export interface IframeWidget extends JsonObject {
  id: string;
  title: string;
  url: string;
  height?: number;
}

export interface IframeWidgetContextValue {
  widget: IframeWidget | null;
  loading: boolean;
  dialogOpen: boolean;
  title: string;
  url: string;
  height: number;
  setTitle: (title: string) => void;
  setUrl: (url: string) => void;
  setHeight: (height: number) => void;
  handleOpenDialog: () => void;
  handleCloseDialog: () => void;
  handleSaveWidget: () => Promise<void>;
  handleClearWidget: () => Promise<void>;
}
