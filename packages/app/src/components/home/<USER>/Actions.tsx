import { IconButton, Tooltip } from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import VisibilityIcon from '@material-ui/icons/Visibility';
import { useMarkdownNotes } from './Context';

export const Actions = () => {
  const { isEditing, toggleEditMode } = useMarkdownNotes();

  return (
    <Tooltip title={isEditing ? "查看" : "编辑"}>
      <IconButton
        aria-label={isEditing ? "查看" : "编辑"}
        onClick={toggleEditMode}
      >
        {isEditing ? <VisibilityIcon /> : <EditIcon />}
      </IconButton>
    </Tooltip>
  );
};
