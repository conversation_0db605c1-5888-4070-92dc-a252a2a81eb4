import { IconButton, Tooltip } from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import { useCustomShortcuts } from './Context';

export const Actions = () => {
  const { handleOpenDialog } = useCustomShortcuts();

  return (
    <Tooltip title="添加快捷链接">
      <IconButton
        aria-label="添加快捷链接"
        onClick={() => handleOpenDialog()}
      >
        <AddIcon />
      </IconButton>
    </Tooltip>
  );
};
