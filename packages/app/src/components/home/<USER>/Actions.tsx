import { IconButton, Tooltip } from '@material-ui/core';
import ClearIcon from '@material-ui/icons/Clear';
import EditIcon from '@material-ui/icons/Edit';
import { useIframeWidget } from './Context';

export const Actions = () => {
  const { widget, handleOpenDialog, handleClearWidget } = useIframeWidget();

  return (
    <>
      <Tooltip title="设置iframe">
        <IconButton
          aria-label="设置iframe"
          onClick={handleOpenDialog}
        >
          <EditIcon />
        </IconButton>
      </Tooltip>
      {widget && (
        <Tooltip title="清除iframe">
          <IconButton
            aria-label="清除iframe"
            onClick={handleClearWidget}
          >
            <ClearIcon />
          </IconButton>
        </Tooltip>
      )}
    </>
  );
};
