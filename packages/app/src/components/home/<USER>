import { useState, useRef } from 'react';
import {
  makeStyles,
  Button,
  Typography,
  Paper,
  IconButton,
} from '@material-ui/core';
import AndroidIcon from '@material-ui/icons/Android';
import CloseIcon from '@material-ui/icons/Close';

// Styles for the Dify Assistant
const useStyles = makeStyles(theme => ({
  assistantContainer: {
    position: 'fixed',
    bottom: 0,
    right: theme.spacing(2),
    zIndex: 999,
    transition: 'transform 0.3s ease-in-out',
    transform: 'translateY(50%)', // 默认半身隐藏
    '&:hover': {
      transform: 'translateY(0)', // 悬停时完全显示
    },
  },
  assistantButton: {
    backgroundColor: '#1C64F2',
    color: 'white',
    borderRadius: '50%',
    width: '56px',
    height: '56px',
    minWidth: 'unset',
    boxShadow: theme.shadows[5],
    '&:hover': {
      backgroundColor: '#1651C9',
    },
  },
  assistantButtonIcon: {
    fontSize: '32px',
  },
  chatContainer: {
    position: 'fixed',
    bottom: theme.spacing(8),
    right: theme.spacing(3),
    width: '30rem',
    height: '40rem',
    zIndex: 1000,
    overflow: 'hidden',
    borderRadius: theme.spacing(1),
    boxShadow: theme.shadows[10],
  },
  chatHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    backgroundColor: '#1C64F2',
    color: 'white',
  },
  chatFrame: {
    width: '100%',
    height: 'calc(100% - 48px)',
    border: 'none',
  },
  closeButton: {
    color: 'white',
  },
}));

interface DifyAssistantProps {
  // Optional props can be added here if needed
}

export const DifyAssistant: React.FC<DifyAssistantProps> = () => {
  const classes = useStyles();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [chatOpen, setChatOpen] = useState(false);

  // Dify configuration
  const difyToken = 'CCloa65fzT4J8W8T';
  const difyBaseUrl = 'https://dify.foneshare.cn';
  const difyEmbedUrl = `${difyBaseUrl}/chat/${difyToken}`;

  // Function to toggle chat window
  const handleToggleChat = () => {
    setChatOpen(prevOpen => !prevOpen);
  };

  // Function to close chat window
  const handleCloseChat = () => {
    setChatOpen(false);
  };

  return (
    <>
      {/* Floating button container with hover effect */}
      <div className={classes.assistantContainer}>
        <Button
          className={classes.assistantButton}
          variant="contained"
          onClick={handleToggleChat}
          aria-label="Open AI Assistant"
        >
          <AndroidIcon className={classes.assistantButtonIcon} />
        </Button>
      </div>

      {/* Embedded Dify Chat Window */}
      {chatOpen && (
        <Paper className={classes.chatContainer}>
          <div className={classes.chatHeader}>
            <Typography variant="subtitle1">纷享百晓生</Typography>
            <IconButton
              className={classes.closeButton}
              onClick={handleCloseChat}
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </div>
          <iframe
            ref={iframeRef}
            src={difyEmbedUrl}
            title="Dify AI Assistant"
            className={classes.chatFrame}
            allow="microphone"
          />
        </Paper>
      )}
    </>
  );
};

export default DifyAssistant;
