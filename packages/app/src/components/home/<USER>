import { Page, Content } from '@backstage/core-components';
import {
  HomePageCompanyLogo,
  HomePageStarredEntities,
  TemplateBackstageLogo,
} from '@backstage/plugin-home';
import { HomePageSearchBar } from '@backstage/plugin-search';
import { SearchContextProvider } from '@backstage/plugin-search-react';
import { Grid, makeStyles } from '@material-ui/core';
import DifyAssistant from './DifyAssistant';


const useStyles = makeStyles(theme => ({
  searchBarInput: {
    maxWidth: '60vw',
    margin: 'auto',
    backgroundColor: theme.palette.background.paper,
    borderRadius: '50px',
    boxShadow: theme.shadows[1],
  },
  searchBarOutline: {
    borderStyle: 'none',
  },
  starredEntitiesContainer: {
    minHeight: '300px',
    '& > div': {
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
    },
    '& .MuiCard-root': {
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
    },
    '& .MuiCardContent-root': {
      flex: 1,
      overflow: 'auto',
    },
  },
}));

export const useLogoStyles = makeStyles(theme => ({
  container: {
    margin: theme.spacing(5, 0),
  },
  svg: {
    width: 'auto',
    height: 100,
  },
  path: {
    fill: '#7df3e1',
  },
}));

export const HomePage = () => {
  const classes = useStyles();
  const { svg, path, container } = useLogoStyles();

  return (
    <SearchContextProvider>
      <Page themeId="home">
        <Content>
          <Grid container justifyContent="center" spacing={2}>
            <HomePageCompanyLogo
              className={container}
              logo={<TemplateBackstageLogo classes={{ svg, path }} />}
            />
            <Grid container item xs={12} justifyContent="center">
              <HomePageSearchBar
                InputProps={{
                  classes: {
                    root: classes.searchBarInput,
                    notchedOutline: classes.searchBarOutline,
                  },
                }}
                placeholder="搜索系统、文档、API..."
              />
            </Grid>
            <Grid container item xs={12}>
              <Grid item xs={12} md={6}>
                <HomePageStarredEntities groupByKind title='我的收藏' noStarredEntitiesMessage='在服务目录里收藏你喜欢的组件，然后在这里快速查看。' />
              </Grid>
              {/* <Grid item xs={12} md={6}>
                <CustomShortcutsCard />
              </Grid> */}
            </Grid>
          </Grid>
        </Content>
      </Page>
      {/* Add the Dify Assistant component */}
      <DifyAssistant />
    </SearchContextProvider>
  );
};