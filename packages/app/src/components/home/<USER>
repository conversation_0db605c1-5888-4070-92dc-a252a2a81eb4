import { createCardExtension } from '@backstage/plugin-home-react';
import { homePlugin } from '@backstage/plugin-home';

// 必须是createCardExtension的组件才能在home页面可选，这是Backstage的约束
export const MarkdownNotesCardComponent = homePlugin.provide(
  createCardExtension({
    name: 'MarkdownNotesCardComponent',
    title: '速记本',
    components: () => import('./MarkdownNotesComponent/index')
      .then(module => ({
        Content: module.Content,
        Actions: module.Actions,
        ContextProvider: module.ContextProvider,
      })),
  }),
);
