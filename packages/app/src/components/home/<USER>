import { Page, Content } from '@backstage/core-components';
import {
  HomePageCompanyLogo,
  TemplateBackstageLogo,
  HomePageStarredEntities,
  CustomHomepageGrid,
  HomePageTopVisited,
  HomePageRecentlyVisited,
} from '@backstage/plugin-home';
import { HomePageSearchBar } from '@backstage/plugin-search';
import { Grid, makeStyles, Typography } from '@material-ui/core';

import { useLogoStyles } from './shared';
import { CustomShortcutsCardComponent } from './CustomShortcutsComponent';
import { MarkdownNotesCardComponent } from './MarkdownNotesComponent';
import { IframeWidgetCardComponent } from './IframeWidgetComponent';
import DifyAssistant from './DifyAssistant';

const defaultConfig = [
  {
    component: 'HomePageSearchBar',
    x: 0,
    y: 0,
    width: 12,
    height: 2,
  },
  {
    component: 'HomePageRecentlyVisited',
    x: 0,
    y: 1,
    width: 6,
    height: 5,
  },
  {
    component: 'HomePageTopVisited',
    x: 6,
    y: 1,
    width: 6,
    height: 5,
  },
  {
    component: 'CustomShortcutsCardComponent',
    x: 0,
    y: 2,
    width: 6,
    height: 5,
  },
  {
    component: 'HomePageStarredEntities',
    x: 6,
    y: 2,
    width: 6,
    height: 5,
  },
  {
    component: 'MarkdownNotesCardComponent',
    x: 0,
    y: 3,
    width: 6,
    height: 5,
  },
];

const useStyles = makeStyles(theme => ({
  searchBarInput: {
    maxWidth: '60vw',
    margin: 'auto',
    backgroundColor: theme.palette.background.paper,
    borderRadius: '50px',
    boxShadow: theme.shadows[1],
  },
  searchBarOutline: {
    borderStyle: 'none',
  },
  noItems: {
    textAlign: 'center',
    padding: theme.spacing(4),
  },
}));

export const CustomizableHomePage = () => {
  const classes = useStyles();
  const { svg, path, container } = useLogoStyles();
  return (
    <Page themeId="home">
      <Content>
        <Grid container justifyContent="center">
          <HomePageCompanyLogo
            className={container}
            logo={<TemplateBackstageLogo classes={{ svg, path }} />}
          />
        </Grid>

        <CustomHomepageGrid config={defaultConfig} breakpoints={{
          xxs: 3,
          xs: 3,
          sm: 6,
          md: 12,
          lg: 12,
          xl: 12,
        }}>
          <HomePageSearchBar
            InputProps={{
              classes: {
                root: classes.searchBarInput,
                notchedOutline: classes.searchBarOutline,
              },
            }}
            placeholder="搜索系统、文档、API..."
          />
          <HomePageRecentlyVisited title='最近访问' numVisitsOpen={4} numVisitsTotal={100} />
          <HomePageTopVisited title='最常访问' numVisitsOpen={4} numVisitsTotal={50} />
          <HomePageStarredEntities title='我的收藏' noStarredEntitiesMessage={
            <Typography variant="body2" className={classes.noItems}>
              在服务目录里点星标收藏你喜欢的组件
            </Typography>
          } />
          <CustomShortcutsCardComponent title='自定义链接' />
          <MarkdownNotesCardComponent title='速记本' />
          <IframeWidgetCardComponent title='iframe' />
        </CustomHomepageGrid>
      </Content>
      <DifyAssistant />
    </Page>
  );
};