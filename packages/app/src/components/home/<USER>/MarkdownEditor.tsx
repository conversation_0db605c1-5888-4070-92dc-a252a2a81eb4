import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Typography,
  makeStyles,
  useTheme,
} from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import MDEditor from '@uiw/react-md-editor';
import '@uiw/react-md-editor/markdown-editor.css';

const useStyles = makeStyles(theme => ({
  dialog: {
    '& .MuiDialog-paper': {
      maxWidth: '60vw',
      width: '400px',
      maxHeight: '80vh',
      height: '400px',
    },
  },
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dialogContent: {
    padding: 0,
    overflow: 'hidden',
  },
  editorContainer: {
    padding: theme.spacing(2),
  },
  dialogActions: {
    justifyContent: 'space-between',
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
}));

interface MarkdownEditorProps {
  open: boolean;
  value: string;
  onChange: (value: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  open,
  value,
  onChange,
  onSave,
  onCancel,
}) => {
  const classes = useStyles();
  const theme = useTheme();

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      className={classes.dialog}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className={classes.dialogTitle} disableTypography>
        <Typography variant="h6">
          Markdown 编辑器
        </Typography>
        <IconButton
          aria-label="关闭"
          onClick={onCancel}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent className={classes.dialogContent}>
        <div className={classes.editorContainer}>
          <MDEditor
            value={value}
            onChange={(val) => onChange(val || '')}
            preview="edit"
            hideToolbar={false}
            visibleDragbar={false}
            data-color-mode={theme.palette.type === 'dark' ? 'dark' : 'light'}
            height={450}
          />
        </div>
      </DialogContent>

      <DialogActions className={classes.dialogActions}>
        <Typography variant="caption" color="textSecondary">
          支持标准 Markdown 语法，包括表格、代码块、链接等
        </Typography>
        <div className={classes.actionButtons}>
          <Button onClick={onCancel} color="default">
            取消
          </Button>
          <Button
            onClick={onSave}
            color="primary"
            variant="contained"
          >
            保存
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
};
