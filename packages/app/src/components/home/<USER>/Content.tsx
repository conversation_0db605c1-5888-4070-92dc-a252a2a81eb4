import {
  Typography,
  makeStyles,
} from '@material-ui/core';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useMarkdownNotes } from './Context';
import { MarkdownEditor } from './MarkdownEditor';

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(1),
    overflowY: 'auto',
  },
  markdownContainer: {
  },
  emptyState: {
    textAlign: 'center',
    padding: theme.spacing(4),
    color: theme.palette.text.secondary,
  },
}));

export const Content = () => {
  const classes = useStyles();
  const {
    note,
    loading,
    isEditing,
    editContent,
    setEditContent,
    handleSaveEdit,
    handleCancelEdit,
  } = useMarkdownNotes();

  if (loading) {
    return <Typography>加载中...</Typography>;
  }

  if (!note) {
    return (
      <Typography variant="body2" className={classes.emptyState}>
        暂无笔记内容，点击编辑按钮开始创建
      </Typography>
    );
  }

  return (
    <>
      <div className={classes.content}>
        <div className={classes.markdownContainer}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {note.content}
          </ReactMarkdown>
        </div>
      </div>

      <MarkdownEditor
        open={isEditing}
        value={editContent}
        onChange={setEditContent}
        onSave={handleSaveEdit}
        onCancel={handleCancelEdit}
      />
    </>
  );
};
