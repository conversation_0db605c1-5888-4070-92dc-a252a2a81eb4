import {
  Typography,
  makeStyles,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@material-ui/core';
import { useIframeWidget } from './Context';

const useStyles = makeStyles(theme => ({
  content: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[2],
  },
  emptyState: {
    textAlign: 'center',
    padding: theme.spacing(4),
    color: theme.palette.text.secondary,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(2),
  },
  previewContainer: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.background.default,
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.divider}`,
  },
  previewIframe: {
    width: '100%',
    height: '200px',
    border: 'none',
    borderRadius: theme.shape.borderRadius,
  },
}));

export const Content = () => {
  const classes = useStyles();
  const {
    widget,
    loading,
    dialogOpen,
    title,
    url,
    setTitle,
    setUrl,
    handleCloseDialog,
    handleSaveWidget,
  } = useIframeWidget();

  if (loading) {
    return <Typography>加载中...</Typography>;
  }

  if (!widget) {
    return (
      <div className={classes.content}>
        <div className={classes.emptyState}>
          <Typography variant="h6" gutterBottom>
            暂无嵌入内容
          </Typography>
          <Typography variant="body2" color="textSecondary">
            点击设置按钮添加iframe内容，允许嵌入的网站有白名单限制，请联系管理员添加
          </Typography>
        </div>

        {/* Setup Dialog */}
        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          aria-labelledby="iframe-dialog-title"
          maxWidth="md"
          fullWidth
        >
          <DialogTitle id="iframe-dialog-title">
            设置iframe内容
          </DialogTitle>
          <DialogContent>
            <form className={classes.form}>
              <TextField
                autoFocus
                margin="dense"
                id="title"
                label="标题"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="输入iframe标题"
              />
              <TextField
                margin="dense"
                id="url"
                label="URL地址"
                type="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://example.com"
                helperText="请输入要嵌入的网页地址"
              />
              {url && (
                <div className={classes.previewContainer}>
                  <Typography variant="subtitle2" gutterBottom>
                    预览
                  </Typography>
                  <iframe
                    src={url}
                    className={classes.previewIframe}
                    title="预览"
                  />
                </div>
              )}
            </form>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog} color="default">
              取消
            </Button>
            <Button onClick={handleSaveWidget} color="primary" variant="contained">
              保存
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }

  return (
    <div className={classes.content}>
      <iframe
        src={widget.url}
        title={widget.title}
        className={classes.iframe}
        style={{ height: `${widget.height}px` }}
      />

      {/* Edit Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="iframe-dialog-title"
        maxWidth="md"
        fullWidth
      >
        <DialogTitle id="iframe-dialog-title">
          编辑iframe内容
        </DialogTitle>
        <DialogContent>
          <form className={classes.form}>
            <TextField
              autoFocus
              margin="dense"
              id="title"
              label="标题"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <TextField
              margin="dense"
              id="url"
              label="URL地址"
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
            />
            {url && (
              <div className={classes.previewContainer}>
                <Typography variant="subtitle2" gutterBottom>
                  预览
                </Typography>
                <iframe
                  src={url}
                  className={classes.previewIframe}
                  title="预览"
                />
              </div>
            )}
          </form>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="default">
            取消
          </Button>
          <Button onClick={handleSaveWidget} color="primary" variant="contained">
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
