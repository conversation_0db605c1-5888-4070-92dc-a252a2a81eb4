
import {
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  makeStyles,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import LinkIcon from '@material-ui/icons/Link';
import { useCustomShortcuts } from './Context';

const useStyles = makeStyles(theme => ({
  content: {
    maxHeight: '400px',
    overflow: 'auto',
  },
  listItem: {
    borderRadius: theme.shape.borderRadius,
    '&:hover': {
      backgroundColor: theme.palette.background.default,
    },
  },
  linkIcon: {
    marginRight: theme.spacing(1),
    color: theme.palette.primary.main,
  },
  noLinks: {
    textAlign: 'center',
    padding: theme.spacing(4),
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(2),
  },
}));

export const Content = () => {
  const classes = useStyles();
  const {
    shortcuts,
    loading,
    dialogOpen,
    editingShortcut,
    title,
    url,
    setTitle,
    setUrl,
    handleOpenDialog,
    handleCloseDialog,
    handleSaveShortcut,
    handleDeleteShortcut,
    handleOpenLink,
  } = useCustomShortcuts();

  if (loading) {
    return <Typography>加载中...</Typography>;
  }

  return (
    <div className={classes.content}>
      {shortcuts.length === 0 ? (
        <Typography variant="body2" className={classes.noLinks}>
          暂无快捷链接，点击"+"添加
        </Typography>
      ) : (
        <List>
          {shortcuts.map((shortcut) => (
            <ListItem
              key={shortcut.id}
              className={classes.listItem}
              button
              onClick={() => handleOpenLink(shortcut.url)}
            >
              <LinkIcon className={classes.linkIcon} fontSize="small" />
              <ListItemText
                primary={shortcut.title}
                title={shortcut.url}
              />
              <ListItemSecondaryAction>
                <Tooltip title="编辑">
                  <IconButton
                    edge="end"
                    aria-label="编辑"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenDialog(shortcut);
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="删除">
                  <IconButton
                    edge="end"
                    aria-label="删除"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteShortcut(shortcut.id);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}

      {/* Add/Edit Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="form-dialog-title"
        maxWidth="sm"
        PaperProps={{
          style: {
            minWidth: 480,
            maxWidth: 520,
          }
        }}
      >
        <DialogTitle id="form-dialog-title">
          {editingShortcut ? '编辑快捷链接' : '添加快捷链接'}
        </DialogTitle>
        <DialogContent>
          <form className={classes.form}>
            <TextField
              autoFocus
              margin="dense"
              id="title"
              label="标题"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <TextField
              margin="dense"
              id="url"
              label="链接地址"
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
            />
          </form>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            取消
          </Button>
          <Button onClick={handleSaveShortcut} color="primary" variant="contained">
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
