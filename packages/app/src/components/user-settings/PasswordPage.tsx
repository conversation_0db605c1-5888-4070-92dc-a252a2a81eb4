import { InfoCard } from '@backstage/core-components';
import List from '@material-ui/core/List';
import Grid from '@material-ui/core/Grid';
import ListItem from '@material-ui/core/ListItem';
import ListItemText from '@material-ui/core/ListItemText';
import ListItemSecondaryAction from '@material-ui/core/ListItemSecondaryAction';
import Link from '@material-ui/core/Link';
import OpenInNewIcon from '@material-ui/icons/OpenInNew';

const PasswordPage = () => {

  return (
    <Grid container direction="row" spacing={3}>
      <Grid item xs={12} md={12}>
        <InfoCard title="如何修改密码" subheader="请点击下方对应的链接修改密码，如有相关问题请联系运维团队。" variant="gridItem">
          <List>
            <ListItem>
              <ListItemText
                primary="线下办公环境 firstshare.cn 域账号"
                secondary="https://pwd.firstshare.cn/index.php"
              />
              <ListItemSecondaryAction>
                <Link
                  href="https://pwd.firstshare.cn/index.php"
                  target="_blank"
                  color="primary"
                >
                  <OpenInNewIcon />
                </Link>
              </ListItemSecondaryAction>
            </ListItem>
            <ListItem>
              <ListItemText
                primary="线上正式环境 foneshare.cn 域账号"
                secondary="https://pwd.foneshare.cn/index.php"
              />
              <ListItemSecondaryAction>
                <Link
                  href="https://pwd.foneshare.cn/index.php"
                  target="_blank"
                  color="secondary"
                >
                  <OpenInNewIcon />
                </Link>
              </ListItemSecondaryAction>
            </ListItem>

            <ListItem>
              <ListItemText
                primary="纷享销客手机号密码，登录后在个人设置修改密码"
                secondary="https://www.fxiaoke.com"
              />
              <ListItemSecondaryAction>
                <Link
                  href="https://www.fxiaoke.com"
                  target="_blank"
                  color="primary"
                >
                  <OpenInNewIcon />
                </Link>
              </ListItemSecondaryAction>
            </ListItem>
          </List>
        </InfoCard>
      </Grid>
    </Grid>
  );
}

export const passwordPage = <PasswordPage />;